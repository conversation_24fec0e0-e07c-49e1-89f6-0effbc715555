#!/usr/bin/env python3
"""
Temporary LangGraph Test Script - Phase 1 Learning
This script demonstrates basic LangGraph concepts with StateGraph, nodes, and edges.
To be deleted after Phase 1 completion.
"""

from typing import TypedDict, Annotated
import operator
from langgraph.graph import StateGraph, START, E<PERSON>


# Define the state structure for our graph
class LangGraphTestState(TypedDict):
    """
    State schema for our test graph.
    
    input_data: The initial data provided to the graph
    processed_data: Data after processing by the first node
    final_result: The final output after all processing
    processing_steps: List of steps taken (demonstrating reducer functionality)
    """
    input_data: str
    processed_data: str
    final_result: str
    # Using Annotated with operator.add to demonstrate state reduction
    processing_steps: Annotated[list[str], operator.add]


def data_processor_node(state: LangGraphTestState) -> dict:
    """
    First node: Process the input data and add analysis.
    
    Args:
        state: Current graph state
        
    Returns:
        dict: State updates to apply
    """
    print(f"📊 Data Processor Node: Processing '{state['input_data']}'")
    
    # Simulate some data processing
    processed = f"PROCESSED[{state['input_data'].upper()}]"
    
    return {
        "processed_data": processed,
        "processing_steps": ["data_processor_executed"]
    }


def result_formatter_node(state: LangGraphTestState) -> dict:
    """
    Second node: Format and finalize the results.
    
    Args:
        state: Current graph state
        
    Returns:
        dict: State updates to apply
    """
    print(f"✨ Result Formatter Node: Formatting '{state['processed_data']}'")
    
    # Create final formatted result
    final = f"FINAL_RESULT: {state['processed_data']} | Steps: {len(state['processing_steps']) + 1}"
    
    return {
        "final_result": final,
        "processing_steps": ["result_formatter_executed"]
    }


def create_langgraph_workflow() -> StateGraph:
    """
    Create and configure the LangGraph workflow.
    
    Returns:
        StateGraph: Compiled graph ready for execution
    """
    print("🔧 Building LangGraph workflow...")
    
    # Initialize StateGraph with our state schema
    workflow = StateGraph(LangGraphTestState)
    
    # Add nodes to the graph
    workflow.add_node("data_processor", data_processor_node)
    workflow.add_node("result_formatter", result_formatter_node)
    
    # Define the graph structure with edges
    # START -> data_processor -> result_formatter -> END
    workflow.add_edge(START, "data_processor")
    workflow.add_edge("data_processor", "result_formatter") 
    workflow.add_edge("result_formatter", END)
    
    # Compile the graph
    compiled_graph = workflow.compile()
    
    print("✅ LangGraph workflow compiled successfully!")
    return compiled_graph


def run_test_scenario(graph: StateGraph, test_input: str) -> dict:
    """
    Run a test scenario through the graph.
    
    Args:
        graph: Compiled LangGraph workflow
        test_input: Test data to process
        
    Returns:
        dict: Final state after graph execution
    """
    print(f"\n🚀 Running test scenario with input: '{test_input}'")
    print("=" * 60)
    
    # Initial state for the graph
    initial_state = {
        "input_data": test_input,
        "processed_data": "",
        "final_result": "",
        "processing_steps": []
    }
    
    # Execute the graph
    final_state = graph.invoke(initial_state)
    
    print("=" * 60)
    print("🎯 Test scenario completed!")
    
    return final_state


def demonstrate_langgraph_concepts():
    """
    Main demonstration function showcasing LangGraph core concepts.
    """
    print("🌟 LangGraph Concepts Demonstration")
    print("=" * 60)
    print("Core Concepts Being Demonstrated:")
    print("• StateGraph: Graph-based workflow execution")
    print("• Nodes: Python functions that process state")
    print("• Edges: Connections defining execution flow")
    print("• State: Shared data structure with reducers")
    print("• Sequential Execution: data_processor → result_formatter")
    print("=" * 60)
    
    # Create the workflow
    graph = create_langgraph_workflow()
    
    # Test scenarios
    test_cases = [
        "Hello LangGraph",
        "Multi-Agent Trading System", 
        "StateGraph Sequential Test"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}/{len(test_cases)}")
        final_state = run_test_scenario(graph, test_input)
        
        # Display results
        print(f"\n📊 Results for Test Case {i}:")
        print(f"  Input Data: {final_state['input_data']}")
        print(f"  Processed Data: {final_state['processed_data']}")
        print(f"  Final Result: {final_state['final_result']}")
        print(f"  Processing Steps: {final_state['processing_steps']}")
        
        if i < len(test_cases):
            print("\n" + "-" * 40)
    
    print(f"\n🎉 LangGraph demonstration completed successfully!")
    print("✅ Key concepts validated:")
    print("  ✓ StateGraph initialization and compilation")
    print("  ✓ Node definition and registration")
    print("  ✓ Sequential edge configuration (START → node1 → node2 → END)")
    print("  ✓ State management with reducers")
    print("  ✓ Graph execution and state updates")


if __name__ == "__main__":
    demonstrate_langgraph_concepts() 
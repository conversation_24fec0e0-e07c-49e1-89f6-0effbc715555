"""
多智能体协调器

负责创建、初始化和协调多个智能体的运行，处理智能体之间的信息流，
并使用Myerson值计算各智能体的贡献
"""
import os
import json
import time
from typing import Dict, List, Any, Tuple, Optional, Set
from datetime import datetime
import importlib
import pandas as pd
import math
import random

# 修改导入路径，使用相对导入
from config import AGENT_TYPES, AGENT_GRAPH, get_run_id
from utils.myerson_calc import MyersonCalculator
from utils.pomdp_optimizer import POMDPOptimizer
from utils.gpo_optimizer import GPOOptimizer
from utils.opro_optimizer import OPROOptimizer
from utils.myerson_opro_optimizer import MyersonOPROOptimizer
from stock_trading_env import StockTradingEnv
from agents.base_agent import BaseAgent

# 导入新增的 LLM 特征函数和新的 POMDP 优化器
from utils.llm_characteristic_function import LLMCharacteristicFunction

class MultiAgentCoordinator:
    """
    多智能体协调器
    
    负责创建、初始化和协调多个智能体的运行
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化多智能体协调器
        
        参数:
            config: 配置参数
        """
        self.config = config
        self.run_id = config.get("run_id", get_run_id())
        
        # 创建运行目录
        self.run_dir = os.path.join("reports", self.run_id)
        os.makedirs(self.run_dir, exist_ok=True)
        
        # 创建日志目录
        self.log_dir = os.path.join("logs", "multi_agent_run", self.run_id)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 保存配置
        self._save_config()
        
        # 创建交易环境
        self.env = StockTradingEnv(config.get("env_config", {}))
        
        # 创建智能体
        self.agents = self._create_agents()
        
        # 创建Myerson值计算器
        self.myerson_calculator = MyersonCalculator(AGENT_GRAPH)
        
        # LLM特征函数对象(延迟初始化)
        self.llm_characteristic_function = None
        
        # 创建优化器
        self.optimizers = {}
        optimizer_type = self.config.get("optimization_config", {}).get("optimizer_type", "pomdp")
        
        if optimizer_type == "gpo":
            self.optimizers = self._create_gpo_optimizers()
        elif optimizer_type == "opro":
            self.optimizers = self._create_opro_optimizers()
        elif optimizer_type == "myerson_opro":
            self.optimizers = self._create_myerson_opro_optimizers()
        else:
            self.optimizers = self._create_pomdp_optimizers()
        
        print(f"已初始化优化器类型: {optimizer_type}")
        
        # 上一天的 Myerson 值（用于计算梯度和Myerson-OPRO奖励）
        self.previous_myerson_values = {agent_id: 0.0 for agent_id in self.agents.keys()}
        self.current_myerson_values = {agent_id: 0.0 for agent_id in self.agents.keys()}
        
        # 运行状态
        self.current_state = None
        self.current_day = 0
        self.done = False
        
        # 运行历史
        self.history = []
        
    def _save_config(self) -> None:
        """保存配置到运行目录"""
        config_file = os.path.join(self.run_dir, "config.json")
        with open(config_file, "w") as f:
            json.dump(self.config, f, indent=2)
            
    def _create_agents(self) -> Dict[str, Any]:
        """
        创建智能体
        
        返回:
            智能体字典，键为智能体ID
        """
        agents = {}
        
        # 导入模型接口
        try:
            from utils.model import default_model
            model_interface = default_model
        except ImportError:
            model_interface = None
            
        # 为每种智能体类型创建实例
        for agent_id, agent_type in AGENT_TYPES.items():
            try:
                # 动态导入智能体类
                module_name = f"agents.{agent_type.lower()}"
                if agent_type in ["bullish_outlook", "bearish_outlook", "neutral_outlook", "trader"]:
                    module_name = f"agents.{agent_type.lower()}_agent"
                
                module = importlib.import_module(module_name)
                
                # 查找智能体类
                agent_class = None
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if isinstance(attr, type) and attr_name.lower().endswith("agent"):
                        agent_class = attr
                        break
                        
                if agent_class:
                    # 构建预期的智能体类名（例如 NewsAnalystAgent）
                    expected_class_name = ''.join(word.capitalize() for word in agent_type.split('_')) + 'Agent'
                    
                    # 在模块中查找预期的智能体类
                    agent_class = getattr(module, expected_class_name, None)
                    
                    if agent_class and issubclass(agent_class, BaseAgent):
                        # 创建智能体实例，传递agent_type参数
                        agent = agent_class(agent_id, agent_type, model_interface)
                        agents[agent_id] = agent
                        print(f"已创建智能体: {agent_id} ({agent_type})")
                    else:
                        print(f"警告: 在模块 {module_name} 中未找到预期的智能体类 {expected_class_name} 或其不是BaseAgent的子类")
            except (ImportError, AttributeError) as e:
                print(f"警告: 无法创建智能体 {agent_id}: {str(e)}")
                
        return agents
        
    def _create_pomdp_optimizers(self) -> Dict[str, POMDPOptimizer]:
        """
        为每个智能体创建POMDP优化器
        
        返回:
            优化器字典，键为智能体ID
        """
        optimizers = {}
        
        optimization_config = self.config.get("optimization_config", {})
        exploration_rate = optimization_config.get("exploration_rate", 0.2)
        
        # 导入反思模块
        from utils.reflection_module import ReflectionModule
        
        for agent_id, agent in self.agents.items():
            # 创建反思模块目录
            reflection_dir = os.path.join("logs", "reflection", agent_id)
            os.makedirs(reflection_dir, exist_ok=True)
            
            # 为每个智能体创建反思模块
            reflection_module = ReflectionModule(
                agent_id=agent_id,
                agent_type=agent.agent_type,
                model_interface=agent.model_interface
            )
            
            # 创建优化器，确保提供 reflection_module 参数
            optimizer = POMDPOptimizer(
                agent_id, 
                agent.current_prompt_template,  # 使用当前提示模板作为 base_prompt
                reflection_module=reflection_module,
                exploration_rate=exploration_rate,
                log_dir=os.path.join("logs", "optimizer", agent_id)
            )
            
            optimizers[agent_id] = optimizer
            
        return optimizers
        
    def _create_gpo_optimizers(self) -> Dict[str, GPOOptimizer]:
        """
        为每个智能体创建GPO优化器
        
        返回:
            优化器字典，键为智能体ID
        """
        optimizers = {}
        
        # 获取模型接口
        try:
            from utils.model import default_model
            model_interface = default_model
            print(f"成功加载默认模型接口以用于GPO优化")
        except ImportError as e:
            print(f"未能导入默认模型接口: {e}, GPO优化将使用智能体自身的模型接口")
            model_interface = None
            
        optimization_config = self.config.get("optimization_config", {})
        max_optimization_steps = optimization_config.get("max_optimization_steps", 20)
        history_prompts_k = optimization_config.get("history_prompts_k", 3)
        initial_max_edit_words = optimization_config.get("initial_max_edit_words", 50)
        plateau_patience = optimization_config.get("plateau_patience", 3)
        
        for agent_id, agent in self.agents.items():
            # 如果全局模型接口不可用，尝试使用智能体自己的模型接口
            agent_model_interface = model_interface if model_interface else agent.model_interface
            
            optimizer = GPOOptimizer(
                agent_id, 
                agent.agent_type,
                model_interface=agent_model_interface,
                max_optimization_steps=max_optimization_steps,
                history_prompts_k=history_prompts_k,
                initial_max_edit_words=initial_max_edit_words,
                plateau_patience=plateau_patience
            )
            optimizers[agent_id] = optimizer
            
        return optimizers

    def _create_opro_optimizers(self) -> Dict[str, 'OPROOptimizer']:
        """
        为每个智能体创建OPRO优化器
        
        返回:
            优化器字典，键为智能体ID
        """
        from utils.opro_optimizer import OPROOptimizer
        
        optimizers = {}
        
        # 获取模型接口
        try:
            from utils.model import default_model
            model_interface = default_model
            print(f"成功加载默认模型接口以用于OPRO优化")
        except ImportError as e:
            print(f"未能导入默认模型接口: {e}, OPRO优化将使用智能体自身的模型接口")
            model_interface = None
            
        optimization_config = self.config.get("optimization_config", {})
        max_optimization_steps = optimization_config.get("max_optimization_steps", 20)
        history_prompts_k = optimization_config.get("history_prompts_k", 8)  # OPRO通常使用更多历史
        max_num_generated_instructions = optimization_config.get("max_num_generated_instructions", 4)
        plateau_patience = optimization_config.get("plateau_patience", 3)
        
        for agent_id, agent in self.agents.items():
            # 如果全局模型接口不可用，尝试使用智能体自己的模型接口
            agent_model_interface = model_interface if model_interface else agent.model_interface
            
            optimizer = OPROOptimizer(
                agent_id, 
                agent.agent_type,
                model_interface=agent_model_interface,
                max_optimization_steps=max_optimization_steps,
                history_prompts_k=history_prompts_k,
                max_num_generated_instructions=max_num_generated_instructions,
                plateau_patience=plateau_patience
            )
            optimizers[agent_id] = optimizer
            
        return optimizers
        
    def _create_myerson_opro_optimizers(self) -> Dict[str, MyersonOPROOptimizer]:
        """
        为每个智能体创建Myerson-OPRO优化器
        
        返回:
            优化器字典，键为智能体ID
        """
        from utils.myerson_opro_optimizer import MyersonOPROOptimizer
        
        optimizers = {}
        
        # 获取模型接口
        try:
            from utils.model import default_model
            model_interface = default_model
            print(f"成功加载默认模型接口以用于Myerson-OPRO优化")
        except ImportError as e:
            print(f"未能导入默认模型接口: {e}, Myerson-OPRO优化将使用智能体自身的模型接口")
            model_interface = None
            
        optimization_config = self.config.get("optimization_config", {})
        max_optimization_steps = optimization_config.get("max_optimization_steps", 20)
        history_prompts_k = optimization_config.get("history_prompts_k", 8)  # Myerson-OPRO通常使用更多历史
        max_num_generated_instructions = optimization_config.get("max_num_generated_instructions", 4)
        plateau_patience = optimization_config.get("plateau_patience", 3)
        
        for agent_id, agent in self.agents.items():
            # 如果全局模型接口不可用，尝试使用智能体自己的模型接口
            agent_model_interface = model_interface if model_interface else agent.model_interface
            
            optimizer = MyersonOPROOptimizer(
                agent_id, 
                agent.agent_type,
                model_interface=agent_model_interface,
                max_optimization_steps=max_optimization_steps,
                history_prompts_k=history_prompts_k,
                max_num_generated_instructions=max_num_generated_instructions,
                plateau_patience=plateau_patience
            )
            optimizers[agent_id] = optimizer
            
        return optimizers
        
    def run(self, num_days: Optional[int] = None) -> Dict[str, Any]:
        """
        运行多智能体系统
        
        参数:
            num_days: 运行天数，如果为None则运行到环境结束
            
        返回:
            运行结果
        """
        # 重置环境
        state, _, _, _ = self.env.reset()
        self.current_state = state
        self.current_day = 0
        self.done = False
        
        # 记录开始时间
        start_time = time.time()
        
        # 运行循环
        while not self.done:
            # 检查是否达到指定天数
            if num_days is not None and self.current_day >= num_days:
                break
                
            # 运行一天
            day_result = self._run_day()
            
            # 记录历史
            self.history.append(day_result)
            
            # 更新状态
            self.current_state = day_result["next_state"]
            self.current_day += 1
            self.done = day_result["done"]
            
            # 更新上一天的 Myerson 值
            if "myerson_values" in day_result:
                self.previous_myerson_values = day_result["myerson_values"]
            
            # 每10天保存一次中间结果
            if self.current_day % 10 == 0:
                self._save_results()
                
        # 保存最终结果
        self._save_results()
        
        # 计算运行时间
        run_time = time.time() - start_time
        
        # 返回运行结果
        result = {
            "run_id": self.run_id,
            "days": self.current_day,
            "final_state": self.current_state,
            "run_time": run_time,
            "daily_returns": {day_result["date"]: {"portfolio_return": day_result["reward"], 
                                                  "benchmark_return": day_result.get("info", {}).get("benchmark_return", 0)} 
                              for day_result in self.history}
        }
        
        # 如果有Myerson值，添加到结果中
        if self.history and "myerson_values" in self.history[-1]:
            result["myerson_values"] = self.history[-1]["myerson_values"]
        
        return result
        
    def _run_day(self) -> Dict[str, Any]:
        """
        运行一天的交易
        
        返回:
            当天的运行结果
        """
        print(f"运行第 {self.current_day + 1} 天")
        
        # 当前状态
        state = self.current_state
        current_date = state["date"]
        
        # **重要修复：保存交易执行前的初始状态，用于Myerson值计算**
        initial_state_for_myerson = {
            'cash': state.get('cash', 0),
            'positions': state.get('positions', {}).copy(),
            'position_values': state.get('position_values', {}).copy(),
            'price_history': state.get('price_history', {}),
            'date': state.get('date', ''),
            'ticker': state.get('ticker', ''),
            'news': state.get('news', []),
            'fundamental': state.get('fundamental', {})
        }
        
        # 显示详细的市场信息
        if "price_history" in state and state["price_history"]:
            print(f"📅 交易日期: {current_date}")
            for stock, price_data in state["price_history"].items():
                if price_data and len(price_data) > 0:
                    latest_price = price_data[-1]  # 最新价格
                    print(f"  📈 {stock}: ${latest_price.get('close', 'N/A'):.2f} (开盘: ${latest_price.get('open', 'N/A'):.2f}, 成交量: {latest_price.get('volume', 'N/A'):,})")
                else:
                    print(f"  📈 {stock}: 无价格数据")
        
        # 显示持仓情况
        cash = state.get("cash", 0)
        positions = state.get("positions", {})
        position_values = state.get("position_values", {})
        total_position_value = sum(position_values.values())
        print(f"💰 当前持仓: 现金 ${cash:.2f}, 股票价值 ${total_position_value:.2f}")
        for stock, shares in positions.items():
            if shares > 0:
                value = position_values.get(stock, 0)
                print(f"  📊 {stock}: {shares} 股 (价值: ${value:.2f})")
        
        # 第一层：初始分析层
        print(f"🔍 第一层：初始分析")
        naa_output = self._run_agent("NAA", state)
        taa_output = self._run_agent("TAA", state)
        faa_output = self._run_agent("FAA", state)
        
        # 更新状态，添加初始分析结果
        state_with_initial = state.copy()
        state_with_initial.update({
            "naa_output": naa_output,
            "taa_output": taa_output,
            "faa_output": faa_output
        })
        
        # 第二层：风险评估与展望层（删除STCA和LTCA，直接从初始分析层到展望层）
        print(f"🎯 第二层：风险评估与展望")
        boa_output = self._run_agent("BOA", state_with_initial)
        beoa_output = self._run_agent("BeOA", state_with_initial)
        noa_output = self._run_agent("NOA", state_with_initial)
        
        # 更新状态，添加风险评估结果
        state_with_risk = state_with_initial.copy()
        state_with_risk.update({
            "boa_output": boa_output,
            "beoa_output": beoa_output,
            "noa_output": noa_output
        })
        
        # 第三层：交易执行层
        print(f"💼 第三层：交易执行")
        tra_output = self._run_agent("TRA", state_with_risk)
        
        # 显示交易决策
        actions = tra_output.get("actions", {})
        print(f"📋 交易决策:")
        if actions:
            for stock, action_value in actions.items():
                if abs(action_value) > 0.001:  # 只显示有意义的交易
                    action_type = "买入" if action_value > 0 else "卖出"
                    print(f"  🔄 {stock}: {action_type} (强度: {abs(action_value):.3f})")
                else:
                    print(f"  ⏸️ {stock}: 保持不变")
        else:
            print(f"  ⚠️ 没有生成任何交易决策")
        
        # 执行交易决策
        print(f"⚡ 执行交易...")
        next_state, reward, done, info = self.env.step(actions)
        
        # 显示交易执行结果
        net_worth = info.get("net_worth", 0)
        daily_return = info.get("daily_return", 0)
        total_return = info.get("total_return", 0)
        new_cash = info.get("cash", cash)
        new_positions = info.get("positions", positions)
        
        print(f"📊 交易执行结果:")
        print(f"  💰 净资产: ${net_worth:.2f} (日收益率: {daily_return:.4f}, 总收益率: {total_return:.4f})")
        print(f"  💵 现金变化: ${cash:.2f} → ${new_cash:.2f} (变动: ${new_cash - cash:.2f})")
        
        # 显示持仓变化
        for stock in set(list(positions.keys()) + list(new_positions.keys())):
            old_pos = positions.get(stock, 0)
            new_pos = new_positions.get(stock, 0)
            if old_pos != new_pos:
                print(f"  📈 {stock}: {old_pos} → {new_pos} 股 (变动: {new_pos - old_pos})")
        
        # **修复：使用交易前的初始状态计算Myerson值**
        print(f"🧮 计算Myerson值...")
        myerson_values = self._calculate_myerson_values(
            initial_state_for_myerson,  # 使用交易前的状态
            state_with_risk,           # 包含所有智能体输出的完整状态
            actions, 
            reward, 
            next_state
        )
        
        # 更新当前的Myerson值
        self.current_myerson_values = myerson_values.copy()
        
        # 生成基于Myerson值的奖励分配（用于传统优化器）
        agent_rewards = self._generate_agent_rewards(myerson_values, reward)
        
        # 更新上一天的Myerson值，供下次计算梯度使用
        self.previous_myerson_values = myerson_values.copy()
        
        # 智能体反思
        reflections = self._run_reflections(agent_rewards)
        
        # 优化智能体提示
        self._optimize_prompts(state, agent_rewards, next_state)
        
        # 记录当天结果
        day_result = {
            "day": self.current_day,
            "date": current_date,
            "state": state,
            "initial_state": initial_state_for_myerson,  # 添加初始状态记录
            "actions": actions,
            "next_state": next_state,
            "reward": reward,
            "done": done,
            "info": info,
            "agent_outputs": {
                "NAA": naa_output,
                "TAA": taa_output,
                "FAA": faa_output,
                "BOA": boa_output,
                "BeOA": beoa_output,
                "NOA": noa_output,
                "TRA": tra_output
            },
            "myerson_values": myerson_values,
            "agent_rewards": agent_rewards,
            "reflections": reflections
        }
        
        # 记录日志
        self._log_day_result(day_result)
        
        return day_result
        
    def _run_agent(self, agent_id: str, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行单个智能体
        
        参数:
            agent_id: 智能体ID
            state: 当前状态
            
        返回:
            智能体输出
        """
        if agent_id not in self.agents:
            print(f"警告: 智能体 {agent_id} 不存在")
            return {}
            
        agent = self.agents[agent_id]
        
        try:
            # 确保状态是可序列化的
            serializable_state = self._make_serializable(state)
            
            # 处理状态
            output = agent.process(serializable_state)
            
            # 确保输出是字典类型
            if not isinstance(output, dict):
                print(f"警告: 智能体 {agent_id} 返回的不是字典类型，而是 {type(output).__name__}，将转换为字典")
                if output is None:
                    return {"error": "智能体返回None"}
                elif isinstance(output, str):
                    return {"response": output}
                else:
                    return {"data": str(output)}
                    
            return output
        except Exception as e:
            print(f"运行智能体 {agent_id} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}
            
    def _calculate_myerson_values(self, initial_state: Dict[str, Any], full_state_with_outputs: Dict[str, Any], actions: Dict[str, float], actual_reward: float, next_state: Dict[str, Any]) -> Dict[str, float]:
        """
        计算Myerson值 - 基于实际运行不同智能体子集
        
        参数:
            initial_state: 交易执行前的初始状态（所有子集的起始点）
            full_state_with_outputs: 包含所有智能体输出的完整状态
            actions: 执行的动作
            actual_reward: 实际获得的奖励
            next_state: 下一状态
            
        返回:
            各智能体的Myerson值
        """
        print(f"  💡 开始实际子集模拟计算Myerson值...")
        print(f"  📊 初始状态: 现金=${initial_state.get('cash', 0):.2f}, 持仓={initial_state.get('positions', {})}")
        
        # 所有智能体列表
        all_agents = set(self.agents.keys())
        print(f"  🤖 所有智能体: {list(all_agents)}")
        
        # 子集价值缓存
        subset_values = {}
        
        # 1. 先计算完整系统的价值（我们已经有了实际的奖励）
        full_set = frozenset(all_agents)
        subset_values[full_set] = actual_reward
        print(f"  🌟 完整系统 {list(full_set)} 价值: {actual_reward:.6f}")
        
        # 2. 计算关键子集的价值（实际模拟运行）
        critical_subsets = []
        
        # 定义关键子集：
        # - 各层级的完整组合
        initial_agents = {"NAA", "TAA", "FAA"}
        outlook_agents = {"BOA", "BeOA", "NOA"}
        trader_agent = {"TRA"}
        
        # 关键子集列表
        critical_subsets.extend([
            # 单个智能体
            frozenset(["TRA"]),  # 仅交易员
            frozenset(["NAA"]),  # 仅新闻分析
            frozenset(["TAA"]),  # 仅技术分析
            frozenset(["FAA"]),  # 仅基本面分析
            frozenset(["BOA"]),  # 仅看多
            frozenset(["BeOA"]), # 仅看空
            frozenset(["NOA"]),  # 仅中立
            
            # 两层组合
            frozenset(["TRA", "BOA"]),    # 交易员 + 看多
            frozenset(["TRA", "BeOA"]),   # 交易员 + 看空
            frozenset(["TRA", "NOA"]),    # 交易员 + 中立
            
            # 三层组合（关键路径）
            frozenset(["NAA", "BOA", "TRA"]),   # 新闻 → 看多 → 交易
            frozenset(["TAA", "BOA", "TRA"]),   # 技术 → 看多 → 交易
            frozenset(["FAA", "BOA", "TRA"]),   # 基本面 → 看多 → 交易
            frozenset(["NAA", "BeOA", "TRA"]),  # 新闻 → 看空 → 交易
            frozenset(["TAA", "BeOA", "TRA"]),  # 技术 → 看空 → 交易
            frozenset(["FAA", "BeOA", "TRA"]),  # 基本面 → 看空 → 交易
            frozenset(["NAA", "NOA", "TRA"]),   # 新闻 → 中立 → 交易
            frozenset(["TAA", "NOA", "TRA"]),   # 技术 → 中立 → 交易
            frozenset(["FAA", "NOA", "TRA"]),   # 基本面 → 中立 → 交易
            
            # 层级组合
            frozenset(initial_agents | trader_agent),  # 初始分析 + 交易员
            frozenset(outlook_agents | trader_agent),  # 展望层 + 交易员
            frozenset(initial_agents | outlook_agents | trader_agent),  # 完整系统
        ])
        
        # 去重并排除已计算的完整集合
        critical_subsets = list(set(critical_subsets))
        critical_subsets = [s for s in critical_subsets if s != full_set]
        
        print(f"  🔬 需要模拟的关键子集数量: {len(critical_subsets)}")
        
        # 3. 实际模拟运行各个子集
        for i, subset in enumerate(critical_subsets):
            subset_list = list(subset)
            print(f"  🧪 模拟子集 {i+1}/{len(critical_subsets)}: {subset_list}")
            
            try:
                # **重要修复：所有子集都从相同的初始状态开始模拟**
                subset_value = self._simulate_agent_subset(subset_list, initial_state, full_state_with_outputs)
                subset_values[subset] = subset_value
                print(f"    💰 子集价值: {subset_value:.6f}")
                
            except Exception as e:
                print(f"    ❌ 模拟失败: {e}")
                # 使用启发式估算作为后备
                subset_values[subset] = self._estimate_subset_value(subset_list, actual_reward)
        
        # 4. 为未模拟的子集提供估算值
        print(f"  📊 为剩余子集提供估算值...")
        for agent in all_agents:
            # 空集
            empty_set = frozenset()
            if empty_set not in subset_values:
                subset_values[empty_set] = 0.0
            
            # 各智能体的单元集合（如果还没有）
            singleton = frozenset([agent])
            if singleton not in subset_values:
                subset_values[singleton] = self._estimate_subset_value([agent], actual_reward)
        
        # 5. 计算Shapley值（基于实际的子集价值）
        print(f"  🧮 基于实际子集价值计算Shapley值...")
        myerson_values = {}
        n = len(all_agents)
        
        # 预计算阶乘值以提高效率
        n_factorial = math.factorial(n)
        
        for agent in all_agents:
            shapley_value = 0.0
            contributions = []
            
            # 遍历所有不包含该智能体的子集
            for subset in subset_values.keys():
                if agent not in subset:
                    # 计算边际贡献
                    subset_with_agent = frozenset(subset | {agent})
                    
                    if subset_with_agent in subset_values:
                        marginal_contribution = subset_values[subset_with_agent] - subset_values[subset]
                        contributions.append(marginal_contribution)
                        
                        # 正确的Shapley值权重: |S|!(n-|S|-1)!/n!
                        s = len(subset)
                        try:
                            s_factorial = math.factorial(s)
                            n_minus_s_minus_1_factorial = math.factorial(n - s - 1)
                            weight = (s_factorial * n_minus_s_minus_1_factorial) / n_factorial
                        except ValueError:
                            # 如果阶乘参数为负数，权重为0
                            weight = 0.0
                        
                        weighted_contribution = weight * marginal_contribution
                        shapley_value += weighted_contribution
            
            myerson_values[agent] = shapley_value
            avg_contribution = sum(contributions) / len(contributions) if contributions else 0
            print(f"    🎯 {agent}: Shapley值={shapley_value:.12f}, 平均边际贡献={avg_contribution:.12f}")
        
        # **修复精度问题：改进归一化逻辑**
        print(f"  🔧 修复精度问题：改进归一化和百分比转换...")
        
        # 6. 检查Shapley值的总和和分布
        total_shapley = sum(myerson_values.values())
        print(f"  📊 原始Shapley值总和: {total_shapley:.12f}")
        
        # 如果总和接近0，说明所有智能体的贡献都很小，使用平均分配
        if abs(total_shapley) < 1e-10:
            print(f"  ⚠️ Shapley值总和接近0，使用平均分配策略")
            equal_share = 1.0 / len(all_agents)
            myerson_values_normalized = {agent: equal_share for agent in all_agents}
        else:
            # 标准归一化：确保总和为1
            myerson_values_normalized = {}
            for agent in all_agents:
                normalized_value = myerson_values[agent] / total_shapley
                myerson_values_normalized[agent] = normalized_value
                print(f"    📐 {agent}: 原始={myerson_values[agent]:.12f}, 归一化={normalized_value:.12f}")
        
        # 验证归一化后的总和
        normalized_sum = sum(myerson_values_normalized.values())
        print(f"  ✅ 归一化后总和: {normalized_sum:.12f}")
        
        # 7. 转换为百分比形式（乘以100），但保持合理的精度
        myerson_values_percentage = {}
        for agent, value in myerson_values_normalized.items():
            percentage = value * 100.0
            myerson_values_percentage[agent] = percentage
        
        # 验证百分比总和
        percentage_sum = sum(myerson_values_percentage.values())
        print(f"  📊 百分比总和: {percentage_sum:.6f}%")
                
        print(f"  ✅ Myerson值计算完成")
        print(f"  📊 最终Myerson值（百分比）:")
        for agent, percentage in myerson_values_percentage.items():
            print(f"    🎯 {agent}: {percentage:.6f}%")
        
        return myerson_values_percentage
            
    def _simulate_agent_subset(self, agent_subset: List[str], initial_state: Dict[str, Any], full_state_with_outputs: Dict[str, Any]) -> float:
        """
        模拟运行指定的智能体子集，计算真实收益率
        
        参数:
            agent_subset: 智能体子集列表
            initial_state: 交易执行前的初始状态（起始点）
            full_state_with_outputs: 包含所有智能体输出的完整状态
            
        返回:
            该子集的实际收益率
        """
        # 如果子集为空，返回0
        if not agent_subset:
            return 0.0
            
        # 如果没有交易员，无法执行交易，返回0
        if "TRA" not in agent_subset:
            return 0.0
            
        try:
            # **使用初始状态作为所有子集的公平起始点**
            subset_state = initial_state.copy()
            
            # 第一层：初始分析层
            initial_outputs = {}
            for agent_id in ["NAA", "TAA", "FAA"]:
                if agent_id in agent_subset:
                    # 如果智能体在子集中，使用其输出（从full_state_with_outputs中获取）
                    output_key = f"{agent_id.lower()}_output"
                    if output_key in full_state_with_outputs:
                        initial_outputs[output_key] = full_state_with_outputs[output_key]
                    else:
                        # 如果找不到输出，重新运行智能体
                        try:
                            output = self.agents[agent_id].process(subset_state)
                            initial_outputs[output_key] = output
                        except Exception as e:
                            print(f"      ⚠️ 重新运行{agent_id}失败: {e}")
                            initial_outputs[output_key] = {"summary": "分析不可用", "actions": {}}
                else:
                    # 子集中没有的智能体，提供空白输出
                    initial_outputs[f"{agent_id.lower()}_output"] = {"summary": "未参与分析", "actions": {}}
            
            # 更新状态
            subset_state.update(initial_outputs)
            
            # 第二层：展望层
            outlook_outputs = {}
            for agent_id in ["BOA", "BeOA", "NOA"]:
                if agent_id in agent_subset:
                    # 如果智能体在子集中，使用其输出
                    output_key = f"{agent_id.lower()}_output"
                    if output_key in full_state_with_outputs:
                        outlook_outputs[output_key] = full_state_with_outputs[output_key]
                    else:
                        # 如果找不到输出，重新运行智能体
                        try:
                            output = self.agents[agent_id].process(subset_state)
                            outlook_outputs[output_key] = output
                        except Exception as e:
                            print(f"      ⚠️ 重新运行{agent_id}失败: {e}")
                            outlook_outputs[output_key] = {"outlook": "neutral", "confidence": 0.5}
                else:
                    # 子集中没有的智能体，提供中立输出
                    outlook_outputs[f"{agent_id.lower()}_output"] = {"outlook": "neutral", "confidence": 0.5}
            
            # 更新状态
            subset_state.update(outlook_outputs)
            
            # 第三层：交易执行层
            if "TRA" in agent_subset:
                # 如果使用完整的交易员输出可能不公平，因为它是基于所有智能体的输出
                # 这里重新运行交易员，基于当前子集的状态
                try:
                    tra_output = self.agents["TRA"].process(subset_state)
                    actions = tra_output.get("actions", {})
                except Exception as e:
                    print(f"      ⚠️ 重新运行TRA失败: {e}")
                    actions = {}
                
                # **修复：避免推进真实环境的日期，使用模拟计算收益率**
                # 保存当前环境状态（包括日期索引）
                original_cash = self.env.cash
                original_positions = self.env.positions.copy()
                original_position_values = self.env.position_values.copy()
                original_current_day_idx = self.env.current_day_idx  # **关键修复：保存日期索引**
                original_current_date = self.env.current_date
                original_previous_net_worth = self.env.previous_net_worth
                original_done = self.env.done
                
                try:
                    # **临时设置环境状态为初始状态**
                    self.env.cash = initial_state['cash']
                    self.env.positions = initial_state['positions'].copy()
                    self.env.position_values = initial_state['position_values'].copy()
                    
                    # 更新环境的净资产（重新计算）
                    self.env._update_net_worth()
                    
                    # **关键修复：模拟交易收益率而不推进日期**
                    reward = self._simulate_trade_without_date_advance(actions, initial_state)
                    
                    return reward  # 返回模拟的收益率
                    
                except Exception as e:
                    print(f"      ⚠️ 计算实际收益率失败: {e}")
                    # 使用改进的启发式估算作为后备
                    return self._improved_estimate_subset_value(agent_subset, actions, initial_state)
                    
                finally:
                    # **关键修复：完全恢复原始环境状态，包括日期索引**
                    self.env.cash = original_cash
                    self.env.positions = original_positions
                    self.env.position_values = original_position_values
                    self.env.current_day_idx = original_current_day_idx  # **恢复日期索引**
                    self.env.current_date = original_current_date
                    self.env.previous_net_worth = original_previous_net_worth
                    self.env.done = original_done
                    if hasattr(self.env, '_update_net_worth'):
                        self.env._update_net_worth()
            else:
                return 0.0
                
        except Exception as e:
            print(f"      ⚠️ 子集模拟完全失败: {e}")
            return 0.0
    
    def _simulate_trade_without_date_advance(self, actions: Dict[str, float], initial_state: Dict[str, Any]) -> float:
        """
        模拟交易收益率而不推进环境日期
        
        参数:
            actions: 交易动作
            initial_state: 初始状态
            
        返回:
            模拟的收益率
        """
        try:
            # 获取当前价格（基于当前日期索引）
            current_prices = {}
            for stock in self.env.stocks:
                if self.env.current_day_idx < len(self.env.price_data[stock]):
                    current_prices[stock] = self.env.price_data[stock].iloc[self.env.current_day_idx]['close']
                elif len(self.env.price_data[stock]) > 0:
                    current_prices[stock] = self.env.price_data[stock].iloc[-1]['close']
                else:
                    current_prices[stock] = 0
            
            # 模拟交易执行，计算净资产变化
            simulated_cash = initial_state['cash']
            simulated_positions = initial_state['positions'].copy()
            initial_net_worth = simulated_cash + sum(
                simulated_positions.get(stock, 0) * current_prices[stock] 
                for stock in self.env.stocks
            )
            
            # 处理全仓卖出标记
            if "__SELL_ALL__" in actions:
                for stock in self.env.stocks:
                    if simulated_positions.get(stock, 0) > 0:
                        shares_to_sell = simulated_positions[stock]
                        price = current_prices[stock]
                        sell_value = shares_to_sell * price
                        fee = sell_value * self.env.trading_fee_rate
                        simulated_cash += sell_value - fee
                        simulated_positions[stock] = 0
                actions = {k: v for k, v in actions.items() if k != "__SELL_ALL__"}
            
            # 处理正常买卖操作
            for stock, action in actions.items():
                if stock not in self.env.stocks:
                    continue
                    
                price = current_prices[stock]
                
                if action > 0:  # 买入
                    cash_to_spend = simulated_cash * 0.99
                    if cash_to_spend > 0 and price > 0:
                        shares_to_buy = int(cash_to_spend / price)
                        if shares_to_buy > 0:
                            buy_value = shares_to_buy * price
                            fee = buy_value * self.env.trading_fee_rate
                            total_cost = buy_value + fee
                            
                            if total_cost <= simulated_cash:
                                simulated_cash -= total_cost
                                simulated_positions[stock] = simulated_positions.get(stock, 0) + shares_to_buy
                
                elif action < 0:  # 卖出
                    shares_to_sell = simulated_positions.get(stock, 0)
                    if shares_to_sell > 0:
                        sell_value = shares_to_sell * price
                        fee = sell_value * self.env.trading_fee_rate
                        simulated_cash += sell_value - fee
                        simulated_positions[stock] = 0
            
            # 计算最终净资产和收益率
            final_net_worth = simulated_cash + sum(
                simulated_positions.get(stock, 0) * current_prices[stock] 
                for stock in self.env.stocks
            )
            
            if initial_net_worth > 0:
                return (final_net_worth / initial_net_worth) - 1
            else:
                return 0.0
                
        except Exception as e:
            print(f"      ⚠️ 模拟交易失败: {e}")
            return 0.0
        
    def _improved_estimate_subset_value(self, agent_subset: List[str], actions: Dict[str, float], state: Dict[str, Any]) -> float:
        """
        改进的子集价值估算方法
        
        参数:
            agent_subset: 智能体子集
            actions: 交易动作
            state: 当前状态
            
        返回:
            估算的收益率
        """
        if not actions:
            return 0.0
            
        # 基于交易动作强度和市场状态的更合理估算
        total_action_strength = sum(abs(v) for v in actions.values())
        
        if total_action_strength == 0:
            return 0.0
            
        # 基于智能体组合质量的基础收益率
        has_initial = any(a in agent_subset for a in ["NAA", "TAA", "FAA"])
        has_outlook = any(a in agent_subset for a in ["BOA", "BeOA", "NOA"])
        has_trader = "TRA" in agent_subset
        
        base_return = 0.0
        if has_trader and has_outlook and has_initial:
            base_return = 0.002  # 2‰ 基础收益率
        elif has_trader and has_outlook:
            base_return = 0.001  # 1‰ 基础收益率
        elif has_trader:
            base_return = 0.0005  # 0.5‰ 基础收益率
            
        # 根据交易强度调整
        return base_return * total_action_strength
        
    def _estimate_subset_value(self, agent_subset: List[str], base_reward: float) -> float:
        """
        估算智能体子集的价值（改进的启发式方法）
        
        参数:
            agent_subset: 智能体子集
            base_reward: 基准奖励（实际收益率）
            
        返回:
            估算的子集价值
        """
        if not agent_subset:
            return 0.0
            
        # 如果没有交易员，返回0
        if "TRA" not in agent_subset:
            return 0.0
            
        # **改进精度：使用更精确的价值比例分配**
        has_initial = any(a in agent_subset for a in ["NAA", "TAA", "FAA"])
        has_outlook = any(a in agent_subset for a in ["BOA", "BeOA", "NOA"])
        has_trader = "TRA" in agent_subset
        
        # 更合理的价值比例分配，使用更精确的数值
        value_ratio = 0.0
        
        if has_trader and has_outlook and has_initial:
            # 完整决策链：应该能获得接近完整的收益
            value_ratio = 0.95  # 95%的基准收益
        elif has_trader and has_outlook:
            # 部分决策链：展望+交易，缺少初始分析
            value_ratio = 0.70  # 70%的基准收益
        elif has_trader and has_initial:
            # 交易员+初始分析，但缺少展望层
            value_ratio = 0.50  # 50%的基准收益
        elif has_trader:
            # 最小决策链：仅交易员，基于默认信号
            value_ratio = 0.25  # 25%的基准收益
        
        # 考虑智能体数量对协同效应的影响
        agent_count_factor = min(len(agent_subset) / len(self.agents), 1.0)
        
        # 综合计算：主要基于价值链完整性，辅以数量因素
        estimated_value = base_reward * (0.8 * value_ratio + 0.2 * agent_count_factor)
        
        # **修复精度问题：减少随机性，使用更稳定的估算**
        # 使用基于智能体组合的确定性调整因子，而不是随机噪声
        stability_factor = 1.0
        if len(agent_subset) == 1:
            stability_factor = 0.9  # 单智能体稍微降低
        elif len(agent_subset) == len(self.agents):
            stability_factor = 1.0  # 完整系统保持不变
        else:
            stability_factor = 0.95  # 部分系统稍微降低
        
        final_value = estimated_value * stability_factor
        
        # 确保数值精度，避免极小值导致的计算问题
        if abs(final_value) < 1e-12:
            final_value = 0.0
            
        return final_value
        
    def _generate_agent_rewards(self, myerson_values: Dict[str, float], total_reward: float) -> Dict[str, float]:
        """
        基于Myerson值生成各智能体的奖励
        
        参数:
            myerson_values: 各智能体的Myerson值
            total_reward: 总奖励
            
        返回:
            各智能体的奖励字典
        """
        # 简单实现：直接使用Myerson值作为奖励
        return myerson_values
        
    def _run_reflections(self, agent_rewards: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """
        运行各智能体的反思
        
        参数:
            agent_rewards: 各智能体的奖励
            
        返回:
            各智能体的反思结果
        """
        reflections = {}
        
        # 检查是否启用反思机制
        if not self.config.get("reflection_config", {}).get("enabled", True):
            print("反思机制已禁用，跳过反思步骤")
            return reflections
            
        for agent_id, reward in agent_rewards.items():
            if agent_id in self.agents:
                try:
                    reflection = self.agents[agent_id].reflect(reward, self.current_state)
                    reflections[agent_id] = reflection
                except Exception as e:
                    print(f"智能体 {agent_id} 反思时出错: {str(e)}")
                    reflections[agent_id] = {"error": str(e)}
                    
        return reflections
        
    def _optimize_prompts(self, state: Dict[str, Any], agent_rewards: Dict[str, float],
                         next_state: Dict[str, Any]) -> None:
        """
        优化各智能体的提示
        
        参数:
            state: 当前状态
            agent_rewards: 各智能体的奖励
            next_state: 下一个状态
        """
        # 检查是否启用优化
        if not self.config.get("optimization_config", {}).get("enabled", False):
            return
            
        # 获取优化器类型
        optimizer_type = self.config.get("optimization_config", {}).get("optimizer_type", "pomdp")
        
        # 检查是否启用 Myerson-POMDP 集成
        use_myerson_pomdp = self.config.get("myerson_config", {}).get("use_myerson_pomdp_integration", False)
        
        # 为每个智能体运行优化
        for agent_id, reward in agent_rewards.items():
            if agent_id in self.optimizers and agent_id in self.agents:
                try:
                    if use_myerson_pomdp and optimizer_type == "pomdp":
                        # 计算本次 Myerson 值相比上次的变化率（梯度）
                        previous_value = self.previous_myerson_values.get(agent_id, 0.0)
                        contribution_gradient = reward - previous_value
                        
                        # 使用增强的 POMDP 优化方法
                        self._run_enhanced_pomdp_optimization(
                            agent_id, 
                            reward, 
                            state, 
                            next_state,
                            contribution_gradient
                        )
                    elif optimizer_type == "gpo":
                        self._run_gpo_optimization(agent_id, reward, state)
                    elif optimizer_type == "opro":
                        self._run_opro_optimization(agent_id, reward, state)
                    elif optimizer_type == "myerson_opro":
                        self._run_myerson_opro_optimization(agent_id, reward, state)
                    else:
                        self._run_pomdp_optimization(agent_id, reward, state, next_state)
                except Exception as e:
                    print(f"优化智能体 {agent_id} 的提示时出错: {str(e)}")
    
    def _run_enhanced_pomdp_optimization(self, agent_id: str, reward: float, 
                                      state: Dict[str, Any], next_state: Dict[str, Any],
                                      contribution_gradient: float) -> None:
        """
        运行增强的POMDP提示优化，使用Myerson值和梯度
        
        参数:
            agent_id: 智能体ID
            reward: 奖励
            state: 当前状态
            next_state: 下一个状态
            contribution_gradient: 贡献梯度
        """
        # 获取当前提示参数
        current_params = self.agents[agent_id].prompt_params
        
        # 构建POMDP状态
        pomdp_state = {
            "day": self.current_day,
            "ticker": state.get("ticker", "Unknown"),
            "date": state.get("date", "Unknown"),
        }
        
        # 获取实际收益率
        actual_return = state.get("info", {}).get("portfolio_return", 0.0)
        
        # 更新优化器
        optimizer = self.optimizers[agent_id]
        if hasattr(optimizer, "reflection_module"):
            # 使用反思模块记录历史
            optimizer.reflection_module.add_history_data(
                prompt=current_params.get("template", ""),
                context=state,
                output=self.agents[agent_id].last_output if hasattr(self.agents[agent_id], "last_output") else "",
                shapley_value=reward,
                reward=contribution_gradient,  # 使用梯度作为奖励信号
                pomdp_state=pomdp_state
            )
        
        # 正常更新优化器
        optimizer.update(current_score=reward, contribution_gradient=contribution_gradient)
        
        # 获取下一组参数
        # next_params = optimizer.get_next_params(current_params)
        # POMDPOptimizer 返回的是下一个完整的提示，而不是参数字典
        next_prompt = optimizer.get_next_prompt()
        
        # 更新智能体参数 (假设智能体的 prompt_params 可以接受一个完整的提示字符串)
        # 或者智能体内部有方法可以根据完整提示更新其参数
        # 如果 agent.prompt_params 是一个期望参数字典的结构，这里需要适配
        # 假设 agent.update_prompt_from_string(next_prompt) 或类似方法存在
        # 或者，如果 agent 的核心是模板，那么直接更新模板
        if hasattr(self.agents[agent_id], 'current_prompt_template'):
            self.agents[agent_id].current_prompt_template = next_prompt
        elif hasattr(self.agents[agent_id], 'prompt_params') and isinstance(self.agents[agent_id].prompt_params, dict) and 'template' in self.agents[agent_id].prompt_params:
            self.agents[agent_id].prompt_params['template'] = next_prompt
        else:
            # 如果没有明确的提示模板属性，尝试将其作为通用参数更新
            # 这可能需要智能体基类有更通用的 set_prompt 方法
            print(f"警告: 智能体 {agent_id} 没有标准的提示模板属性来更新。尝试更新 prompt_params['template']。")
            if isinstance(self.agents[agent_id].prompt_params, dict):
                 self.agents[agent_id].prompt_params['template'] = next_prompt
            else:
                 print(f"错误: 智能体 {agent_id} 的 prompt_params 不是字典，无法更新提示。")
        
    def _run_pomdp_optimization(self, agent_id: str, reward: float, 
                              state: Dict[str, Any], next_state: Dict[str, Any]) -> None:
        """
        运行POMDP提示优化
        
        参数:
            agent_id: 智能体ID
            reward: 奖励
            state: 当前状态
            next_state: 下一个状态
        """
        # 获取当前提示参数
        current_params = self.agents[agent_id].prompt_params
        
        # 假设普通的POMDP优化也使用Myerson值作为分数，并且梯度是相对上一期的变化
        previous_value = self.previous_myerson_values.get(agent_id, 0.0)
        contribution_gradient = reward - previous_value # reward is current_value here

        self.optimizers[agent_id].update(current_score=reward, contribution_gradient=contribution_gradient)
        
        # 获取下一组参数
        # next_params = self.optimizers[agent_id].get_next_params(current_params)
        next_prompt = self.optimizers[agent_id].get_next_prompt()
        
        # 更新智能体参数
        if hasattr(self.agents[agent_id], 'current_prompt_template'):
            self.agents[agent_id].current_prompt_template = next_prompt
        elif hasattr(self.agents[agent_id], 'prompt_params') and isinstance(self.agents[agent_id].prompt_params, dict) and 'template' in self.agents[agent_id].prompt_params:
            self.agents[agent_id].prompt_params['template'] = next_prompt
        else:
            print(f"警告: 智能体 {agent_id} 没有标准的提示模板属性来更新。尝试更新 prompt_params['template']。")
            if isinstance(self.agents[agent_id].prompt_params, dict):
                 self.agents[agent_id].prompt_params['template'] = next_prompt
            else:
                 print(f"错误: 智能体 {agent_id} 的 prompt_params 不是字典，无法更新提示。")
        
    def _run_gpo_optimization(self, agent_id: str, reward: float, state: Dict[str, Any]) -> None:
        """
        运行GPO提示优化
        
        参数:
            agent_id: 智能体ID
            reward: 奖励
            state: 当前状态
        """
        # 获取当前完整提示模板
        agent = self.agents[agent_id]
        current_prompt = agent.current_prompt_template if hasattr(agent, 'current_prompt_template') else ""
        
        if not current_prompt:
            # 如果不存在完整提示模板，则尝试构建一个
            try:
                print(f"智能体 {agent_id} 没有当前提示模板，尝试获取默认模板...")
                default_template = agent.get_default_prompt_template()
                if default_template:
                    current_prompt = default_template
                    # 将默认模板保存到agent的current_prompt_template属性
                    agent.current_prompt_template = default_template
                    print(f"为智能体 {agent_id} 初始化提示模板成功")
                else:
                    print(f"警告: 智能体 {agent_id} 的默认提示模板为空")
                    # 创建一个简单的默认模板
                    default_template = f"""
你是一个专业的{agent.agent_type}智能体，ID为{agent.agent_id}。
请分析提供的数据并生成专业的分析报告。
"""
                    agent.current_prompt_template = default_template
                    current_prompt = default_template
                    print(f"为智能体 {agent_id} 创建了简单的默认提示模板")
            except Exception as e:
                print(f"警告: 智能体 {agent_id} 获取默认提示模板失败: {e}")
                print(f"尝试为智能体 {agent_id} 创建基本提示模板")
                # 创建一个基本的提示模板
                basic_template = f"""
你是一个专业的{agent.agent_type}智能体，ID为{agent.agent_id}。
请分析提供的数据并生成专业的分析报告。
"""
                agent.current_prompt_template = basic_template
                current_prompt = basic_template
                print(f"为智能体 {agent_id} 创建了基本提示模板")
        
        # 从状态中提取任务示例
        task_examples = self._extract_task_examples_for_agent(agent_id, state)
        
        # 更新GPO优化器
        optimizer = self.optimizers[agent_id]
        
        # 规范化奖励得分到0-100范围
        score = min(max(reward * 100, 0), 100)
        
        try:
            # 更新优化轨迹
            optimizer.update(current_prompt, score, task_examples)
            
            # 生成优化的提示
            optimization_result = optimizer.get_optimized_prompt(
                current_prompt, 
                score, 
                task_examples,
                num_candidates=1  # 可以配置生成多个候选项
            )
            
            print(f"智能体 {agent_id} 的GPO优化状态: {optimization_result.get('status')}, {optimization_result.get('details')}")
            
            if "optimized_prompt" in optimization_result and optimization_result["status"] == "success":
                # 更新智能体的提示模板
                agent.current_prompt_template = optimization_result["optimized_prompt"]
                print(f"已成功使用GPO优化智能体 {agent_id} 的提示")
            else:
                # 当状态不为success时，检查是否有plateau出现
                if optimization_result.get('status') == "plateau" and "best_prompt" in optimization_result:
                    # 确保使用最佳提示
                    agent.current_prompt_template = optimization_result["best_prompt"]
                    print(f"智能体 {agent_id} 优化达到停滞状态，使用最佳提示")
        except Exception as e:
            print(f"智能体 {agent_id} 的GPO优化过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
    def _run_opro_optimization(self, agent_id: str, reward: float, state: Dict[str, Any]) -> None:
        """
        运行OPRO提示优化
        
        参数:
            agent_id: 智能体ID
            reward: 奖励
            state: 当前状态
        """
        # 获取当前完整提示模板
        agent = self.agents[agent_id]
        current_prompt = agent.current_prompt_template if hasattr(agent, 'current_prompt_template') else ""
        
        if not current_prompt:
            # 如果不存在完整提示模板，则尝试构建一个
            try:
                print(f"智能体 {agent_id} 没有当前提示模板，尝试获取默认模板...")
                default_template = agent.get_default_prompt_template()
                if default_template:
                    current_prompt = default_template
                    # 将默认模板保存到agent的current_prompt_template属性
                    agent.current_prompt_template = default_template
                    print(f"为智能体 {agent_id} 初始化提示模板成功")
                else:
                    print(f"警告: 智能体 {agent_id} 的默认提示模板为空")
                    # 创建一个简单的默认模板
                    default_template = f"""
你是一个专业的{agent.agent_type}智能体，ID为{agent.agent_id}。
请分析提供的数据并生成专业的分析报告。
"""
                    agent.current_prompt_template = default_template
                    current_prompt = default_template
                    print(f"为智能体 {agent_id} 创建了简单的默认提示模板")
            except Exception as e:
                print(f"警告: 智能体 {agent_id} 获取默认提示模板失败: {e}")
                print(f"尝试为智能体 {agent_id} 创建基本提示模板")
                # 创建一个基本的提示模板
                basic_template = f"""
你是一个专业的{agent.agent_type}智能体，ID为{agent.agent_id}。
请分析提供的数据并生成专业的分析报告。
"""
                agent.current_prompt_template = basic_template
                current_prompt = basic_template
                print(f"为智能体 {agent_id} 创建了基本提示模板")
        
        # 从状态中提取任务示例
        task_examples = self._extract_task_examples_for_agent(agent_id, state)
        
        # 更新OPRO优化器
        optimizer = self.optimizers[agent_id]
        
        # 规范化奖励得分到0-100范围
        score = min(max(reward * 100, 0), 100)
        
        try:
            # 更新优化轨迹
            optimizer.update(current_prompt, score, task_examples)
            
            # 生成优化的提示
            optimization_result = optimizer.get_optimized_prompt(
                current_prompt, 
                score, 
                task_examples,
                num_candidates=1  # 可以配置生成多个候选项
            )
            
            print(f"智能体 {agent_id} 的OPRO优化状态: {optimization_result.get('status')}, {optimization_result.get('details')}")
            
            if "optimized_prompt" in optimization_result and optimization_result["status"] == "success":
                # 更新智能体的提示模板
                agent.current_prompt_template = optimization_result["optimized_prompt"]
                print(f"已成功使用OPRO优化智能体 {agent_id} 的提示")
            else:
                # 当状态不为success时，检查是否有plateau出现
                if optimization_result.get('status') == "plateau" and "best_prompt" in optimization_result:
                    # 确保使用最佳提示
                    agent.current_prompt_template = optimization_result["best_prompt"]
                    print(f"智能体 {agent_id} 优化达到停滞状态，使用最佳提示")
        except Exception as e:
            print(f"智能体 {agent_id} 的OPRO优化过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
    def _run_myerson_opro_optimization(self, agent_id: str, reward: float, state: Dict[str, Any]) -> None:
        """
        运行Myerson-OPRO提示优化
        
        参数:
            agent_id: 智能体ID
            reward: Myerson值奖励（已经是百分比形式）
            state: 当前状态
        """
        # 获取当前完整提示模板
        agent = self.agents[agent_id]
        current_prompt = agent.current_prompt_template if hasattr(agent, 'current_prompt_template') else ""
        
        if not current_prompt:
            # 如果不存在完整提示模板，则尝试构建一个
            try:
                print(f"智能体 {agent_id} 没有当前提示模板，尝试获取默认模板...")
                default_template = agent.get_default_prompt_template() if hasattr(agent, 'get_default_prompt_template') else None
                if default_template:
                    current_prompt = default_template
                    agent.current_prompt_template = default_template
                    print(f"为智能体 {agent_id} 初始化提示模板成功")
                else:
                    print(f"警告: 智能体 {agent_id} 的默认提示模板为空")
                    # 创建一个简单的默认模板
                    default_template = f"""你是一个专业的{agent.agent_type}智能体，ID为{agent.agent_id}。
请分析提供的数据并生成专业的分析报告。"""
                    agent.current_prompt_template = default_template
                    current_prompt = default_template
                    print(f"为智能体 {agent_id} 创建了简单的默认提示模板")
            except Exception as e:
                print(f"警告: 智能体 {agent_id} 获取默认提示模板失败: {e}")
                # 创建一个基本的提示模板
                basic_template = f"""你是一个专业的{agent.agent_type}智能体，ID为{agent.agent_id}。
请分析提供的数据并生成专业的分析报告。"""
                agent.current_prompt_template = basic_template
                current_prompt = basic_template
                print(f"为智能体 {agent_id} 创建了基本提示模板")
        
        # 从状态中提取任务示例
        task_examples = self._extract_task_examples_for_agent(agent_id, state)
        
        # 更新Myerson-OPRO优化器
        optimizer = self.optimizers[agent_id]
        
        # 计算传统评分（基于一些启发式规则）
        traditional_score = 50.0  # 基础分数
        
        # 根据智能体输出质量调整传统评分
        if hasattr(agent, 'last_output') and agent.last_output:
            output = agent.last_output
            if isinstance(output, dict):
                # 检查输出的完整性和质量
                if 'summary' in output and len(output.get('summary', '')) > 10:
                    traditional_score += 10
                if 'actions' in output and output.get('actions'):
                    traditional_score += 10
                if 'confidence' in output and output.get('confidence', 0) > 0.5:
                    traditional_score += 10
        
        # 获取当前智能体的Myerson值（已经是百分比形式）
        current_myerson_value = self.current_myerson_values.get(agent_id, 0.0)
        
        print(f"🔧 Myerson-OPRO优化智能体 {agent_id}:")
        print(f"  📊 当前Myerson值: {current_myerson_value:.6f}")
        print(f"  📈 传统评分: {traditional_score:.1f}")
        print(f"  📝 当前提示长度: {len(current_prompt)} 字符")
        print(f"  🎯 优化器当前步骤: {optimizer.current_step}")
        
        try:
            # **关键修复：直接调用update方法，它会内部调用update_myerson_value**
            print(f"  🔄 调用optimizer.update()...")
            optimizer.update(current_prompt, traditional_score, current_myerson_value, task_examples)
            
            # 生成优化的提示
            print(f"  🚀 生成优化提示...")
            optimization_result = optimizer.get_optimized_prompt(
                current_prompt, 
                traditional_score,
                current_myerson_value,
                task_examples,
                num_candidates=1
            )
            
            print(f"  🎯 优化状态: {optimization_result.get('status')}")
            print(f"  💡 详情: {optimization_result.get('details')}")
            print(f"  🏆 当前Myerson奖励: {optimizer.current_myerson_reward:.6f}")
            print(f"  📊 最佳Myerson奖励: {optimizer.best_myerson_reward:.6f}")
            
            if "optimized_prompt" in optimization_result and optimization_result["status"] == "success":
                # 更新智能体的提示模板
                old_prompt_preview = current_prompt[:100] + "..." if len(current_prompt) > 100 else current_prompt
                new_prompt_preview = optimization_result["optimized_prompt"][:100] + "..." if len(optimization_result["optimized_prompt"]) > 100 else optimization_result["optimized_prompt"]
                
                agent.current_prompt_template = optimization_result["optimized_prompt"]
                print(f"  ✅ 已成功优化智能体 {agent_id} 的提示")
                print(f"    📝 旧提示预览: {old_prompt_preview}")
                print(f"    🆕 新提示预览: {new_prompt_preview}")
            else:
                # 当状态不为success时，检查是否有plateau或其他状态
                if optimization_result.get('status') in ["plateau", "max_steps"] and "best_prompt" in optimization_result:
                    # 确保使用最佳提示
                    if optimization_result["best_prompt"] != current_prompt:
                        agent.current_prompt_template = optimization_result["best_prompt"]
                        print(f"  🔄 智能体 {agent_id} 优化状态: {optimization_result.get('status')}，使用最佳提示")
                    else:
                        print(f"  📌 智能体 {agent_id} 优化状态: {optimization_result.get('status')}，当前提示已是最佳")
                else:
                    print(f"  ⚠️ 智能体 {agent_id} 优化未产生新提示，保持当前提示")
                    
        except Exception as e:
            print(f"  ❌ 智能体 {agent_id} 的Myerson-OPRO优化过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
    def _extract_task_examples_for_agent(self, agent_id: str, state: Dict[str, Any]) -> List[str]:
        """
        从状态中提取适合该智能体的任务示例
        
        参数:
            agent_id: 智能体ID
            state: 当前状态
            
        返回:
            任务示例列表
        """
        # 这里根据不同的智能体类型和可用的状态信息提取适当的任务示例
        examples = []
        
        # 根据智能体类型选择相关的示例数据
        agent_type = AGENT_TYPES.get(agent_id, "")
        
        if agent_type == "news_analyst":
            # 为新闻分析智能体提取示例
            if "news" in state and len(state["news"]) > 0:
                news_sample = state["news"][:2]  # 取前两条新闻作为示例
                examples.append(f"分析以下新闻: {news_sample}")
        elif agent_type == "technical_analyst":
            # 为技术分析智能体提取示例
            if "prices" in state and state["prices"]:
                ticker = state.get("ticker", "未知")
                examples.append(f"分析{ticker}的价格走势")
        elif agent_type == "trader":
            # 为交易智能体提取示例
            if "boa_output" in state and "beoa_output" in state and "noa_output" in state:
                examples.append("基于多个展望分析生成交易决策")
                
        # 如果没有特定示例，提供一个通用示例
        if not examples:
            examples.append(f"作为{agent_type}智能体，生成分析报告")
            
        return examples
        
    def _log_day_result(self, day_result: Dict[str, Any]) -> None:
        """
        记录每日结果
        
        参数:
            day_result: 当日结果
        """
        # 记录到日志文件
        log_file = os.path.join(self.log_dir, f"day_{day_result['day']:04d}.json")
        with open(log_file, "w") as f:
            # 创建可序列化的副本
            serializable_result = self._make_serializable(day_result)
            json.dump(serializable_result, f, indent=2)
            
    def _make_serializable(self, obj: Any) -> Any:
        """
        确保对象可JSON序列化
        
        参数:
            obj: 输入对象
            
        返回:
            可序列化的对象
        """
        if isinstance(obj, dict):
            result = {}
            for k, v in obj.items():
                new_k = k
                # 更明确地检查 pandas Timestamp 类型并转换为字符串
                if isinstance(k, pd.Timestamp):
                    new_k = k.isoformat()
                elif hasattr(k, 'isoformat'): # 处理其他 datetime-like 对象
                    new_k = k.isoformat()
                elif isinstance(k, (int, float, str, bool)) or k is None:
                    new_k = k # 保留原始键如果已经是兼容类型
                else:
                    # 对于其他未知类型的键，尝试转换为字符串，并发出警告
                    # print(f"警告: _make_serializable 遇到未知类型的字典键: {type(k)}。尝试转换为字符串。")
                    new_k = str(k)
                result[new_k] = self._make_serializable(v)
            return result
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, 'items') and callable(obj.items):  # 处理类字典对象(如Series)
            return {str(k): self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (int, float, str, bool, type(None))):
            return obj
        else:
            # 特殊处理日期时间类型（包括pandas.Timestamp）
            if hasattr(obj, 'isoformat'):
                try:
                    return obj.isoformat()
                except:
                    pass # 如果 isoformat 失败，则继续尝试其他方法
            
            # 专门处理pandas的Timestamp类型 (再次检查，以防上面的 hasattr 失败)
            if 'pandas' in str(type(obj)) and 'Timestamp' in str(type(obj)):
                 try:
                    return obj.isoformat()
                 except:
                    pass 
                    
            # 处理其他pandas类型 (如 Series, DataFrame)
            if hasattr(obj, 'to_dict'): # Pandas Series/DataFrame 通常有 to_dict
                try:
                    dict_data = obj.to_dict()
                    return self._make_serializable(dict_data)
                except:
                    pass
                    
            # 尝试转换为字符串
            try:
                return str(obj)
            except:
                return "不可序列化对象"
                
    def _save_results(self) -> None:
        """保存运行结果"""
        # 保存历史
        history_file = os.path.join(self.run_dir, "history.json")
        with open(history_file, "w") as f:
            json.dump(self.history, f, indent=2, default=self._make_serializable)
            
        # 保存智能体记忆 (如果需要)
        # ...
        
        # 保存提示词历史
        self._save_prompt_history()
        
    def _save_prompt_history(self) -> None:
        """
        保存所有智能体的提示词历史到JSON文件
        """
        prompt_history_data = {}
        for agent_id, agent in self.agents.items():
            prompt_history_data[agent_id] = agent.get_prompt_history()
            
        prompt_history_file = os.path.join(self.run_dir, "prompt_history.json")
        with open(prompt_history_file, "w", encoding="utf-8") as f:
            json.dump(prompt_history_data, f, indent=2, default=self._make_serializable, ensure_ascii=False)
            
    def _calculate_agent_performance(self) -> Dict[str, Dict[str, float]]:
        """
        计算智能体表现（例如总收益、Myerson值平均值等）
        
        返回:
            各智能体的表现统计字典
        """
        performance = {}
        
        for agent_id in self.agents.keys():
            # 收集该智能体的所有奖励
            rewards = [day["agent_rewards"].get(agent_id, 0) for day in self.history if "agent_rewards" in day]
            
            if rewards:
                performance[agent_id] = {
                    "total_reward": sum(rewards),
                    "avg_reward": sum(rewards) / len(rewards),
                    "min_reward": min(rewards),
                    "max_reward": max(rewards)
                }
            else:
                performance[agent_id] = {
                    "total_reward": 0,
                    "avg_reward": 0,
                    "min_reward": 0,
                    "max_reward": 0
                }
                
        return performance 
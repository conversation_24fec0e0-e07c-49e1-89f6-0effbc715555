#!/usr/bin/env python3
"""
综合修复验证测试脚本

验证所有修复是否正确工作：
1. DataFrame布尔值错误修复
2. JSON序列化增强
3. 数据格式验证工具
4. 增强日志系统
5. 结果最终化修复
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path

def test_dataframe_boolean_fix():
    """测试DataFrame布尔值错误修复"""
    print("=" * 60)
    print("测试 1: DataFrame布尔值错误修复")
    print("=" * 60)
    
    try:
        # 模拟问题场景
        ohlcv_data = {
            'AAPL': {
                'date': {'0': '2025-01-02T00:00:00', '1': '2025-01-03T00:00:00'},
                'open': {'0': 248.93, '1': 243.36},
                'high': {'0': 249.1, '1': 244.18},
                'low': {'0': 241.8201, '1': 241.89},
                'close': {'0': 243.85, '1': 243.36},
                'adj_close': {'0': 243.85, '1': 243.36},
                'volume': {'0': 55740731, '1': 40244114}
            }
        }
        
        # 测试修复后的逻辑
        for pos_ticker, data in ohlcv_data.items():
            if pos_ticker in ohlcv_data and ohlcv_data[pos_ticker] is not None:
                ticker_data = ohlcv_data[pos_ticker]
                has_data = False
                
                if isinstance(ticker_data, dict):
                    if 'data' in ticker_data and ticker_data['data']:
                        has_data = True
                    elif any(key in ticker_data for key in ['close', 'Close']):
                        has_data = True
                else:
                    try:
                        has_data = not ticker_data.empty
                    except:
                        has_data = False
                
                if has_data:
                    print(f"✅ {pos_ticker}: 数据检查通过")
                else:
                    print(f"❌ {pos_ticker}: 数据检查失败")
        
        print("✅ DataFrame布尔值错误修复测试通过")
        return True
        
    except Exception as e:
        print(f"❌ DataFrame布尔值错误修复测试失败: {e}")
        return False

def test_enhanced_json_serialization():
    """测试增强的JSON序列化"""
    print("\n" + "=" * 60)
    print("测试 2: 增强JSON序列化")
    print("=" * 60)
    
    try:
        from utils.json_serializer import safe_json_dumps, _deep_clean_for_json
        import json
        
        # 测试复杂的嵌套对象
        complex_obj = {
            'timestamp': pd.Timestamp('2024-01-01 10:30:00'),
            'dataframe': pd.DataFrame({
                'date': pd.date_range('2024-01-01', periods=3),
                'price': [100.0, 101.5, 99.8]
            }),
            'nested': {
                'array': np.array([1, 2, 3]),
                'scalar': np.float64(3.14),
                'timestamp': pd.Timestamp.now()
            }
        }
        
        # 测试safe_json_dumps
        json_str = safe_json_dumps(complex_obj)
        print("✅ 复杂对象序列化成功")
        
        # 验证序列化结果是有效的JSON
        parsed = json.loads(json_str)
        print("✅ 序列化结果是有效JSON")
        
        # 测试_deep_clean_for_json
        cleaned_obj = _deep_clean_for_json(complex_obj)
        json_str2 = json.dumps(cleaned_obj)
        print("✅ 深度清理序列化成功")
        
        # 测试错误处理
        class UnserializableClass:
            def __init__(self):
                self.data = "test"
        
        unserializable_obj = {
            'good_data': 'test',
            'bad_data': UnserializableClass()
        }
        
        json_str3 = safe_json_dumps(unserializable_obj)
        print("✅ 错误处理序列化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强JSON序列化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_format_validator():
    """测试数据格式验证工具"""
    print("\n" + "=" * 60)
    print("测试 3: 数据格式验证工具")
    print("=" * 60)
    
    try:
        from utils.data_format_validator import (
            validate_ohlcv_format, 
            convert_dict_to_dataframe,
            ensure_dataframe_format,
            get_latest_price,
            safe_dataframe_check
        )
        
        # 测试字典格式数据
        dict_data = {
            'date': {'0': '2025-01-02', '1': '2025-01-03'},
            'open': {'0': 248.93, '1': 243.36},
            'high': {'0': 249.1, '1': 244.18},
            'low': {'0': 241.8201, '1': 241.89},
            'close': {'0': 243.85, '1': 243.36}
        }
        
        # 验证格式
        is_valid = validate_ohlcv_format(dict_data)
        print(f"✅ 格式验证: {is_valid}")
        
        # 转换为DataFrame
        df = convert_dict_to_dataframe(dict_data)
        print(f"✅ 转换为DataFrame: {df.shape}")
        
        # 确保DataFrame格式
        df2 = ensure_dataframe_format(dict_data)
        print(f"✅ 确保DataFrame格式: {df2.shape}")
        
        # 获取最新价格
        latest_price = get_latest_price(dict_data, 'close')
        print(f"✅ 获取最新价格: {latest_price}")
        
        # 安全检查
        is_safe = safe_dataframe_check(df)
        print(f"✅ 安全检查: {is_safe}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据格式验证工具测试失败: {e}")
        return False

def test_enhanced_logging_system():
    """测试增强日志系统"""
    print("\n" + "=" * 60)
    print("测试 4: 增强日志系统")
    print("=" * 60)
    
    try:
        from utils.enhanced_logger import EnhancedLogger
        
        # 创建测试日志目录
        test_log_dir = "test_comprehensive_logs"
        test_run_id = f"comprehensive_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger = EnhancedLogger(test_log_dir, test_run_id)
        
        # 测试各种日志记录
        logger.log_workflow_state("test_node", {"test": "data"})
        
        # 测试所有智能体的日志记录
        for agent_id in ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]:
            logger.log_agent_input(agent_id, {"input": f"test_data_{agent_id}"}, "2024-01-01")
            logger.log_agent_output(agent_id, {"output": f"test_result_{agent_id}"}, 1.5, "2024-01-01")
        
        logger.log_error("test_component", Exception("test error"), {"context": "test"})
        logger.log_performance_metrics({"test_metric": 0.85})
        
        # 确保所有日志文件都已创建
        file_status = logger.ensure_all_log_files_created()
        print(f"✅ 日志文件状态: {file_status}")
        
        # 获取日志摘要
        log_summary = logger.get_log_summary()
        files_created = log_summary["files_created"]
        
        print(f"✅ 生成了 {len(files_created)} 个日志文件")
        for file_info in files_created:
            print(f"   - {file_info['file']}: {file_info['lines']} 行")
        
        # 验证是否生成了所有必需的日志文件
        required_files = ["workflow_states.jsonl"] + [f"{agent}_input.jsonl" for agent in ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]] + [f"{agent}_output.jsonl" for agent in ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]]
        
        created_file_names = [f['file'] for f in files_created]
        missing_files = [f for f in required_files if f not in created_file_names]
        
        if not missing_files:
            print("✅ 所有必需的日志文件都已创建")
        else:
            print(f"⚠️ 缺少日志文件: {missing_files}")
        
        # 清理测试日志
        import shutil
        if os.path.exists(test_log_dir):
            shutil.rmtree(test_log_dir)
        
        return len(missing_files) == 0
        
    except Exception as e:
        print(f"❌ 增强日志系统测试失败: {e}")
        return False

def test_portfolio_value_calculation():
    """测试投资组合价值计算修复"""
    print("\n" + "=" * 60)
    print("测试 5: 投资组合价值计算修复")
    print("=" * 60)
    
    try:
        from utils.langgraph_state import LangGraphState
        
        # 创建测试状态
        state = LangGraphState(
            current_date="2025-01-02",
            ticker="AAPL",
            cash=10000.0,
            positions={"AAPL": 100}
        )
        
        # 测试字典格式的OHLCV数据
        state.ohlcv_data = {
            "AAPL": {
                'date': {'0': '2025-01-02T00:00:00', '1': '2025-01-03T00:00:00'},
                'open': {'0': 248.93, '1': 243.36},
                'high': {'0': 249.1, '1': 244.18},
                'low': {'0': 241.8201, '1': 241.89},
                'Close': {'0': 243.85, '1': 243.36}
            }
        }
        
        # 测试投资组合价值计算
        portfolio_value = state.get_total_portfolio_value()
        expected_value = 10000.0 + 100 * 243.36  # cash + position_value
        
        print(f"✅ 投资组合价值计算: ${portfolio_value:.2f}")
        print(f"   预期值: ${expected_value:.2f}")
        
        if abs(portfolio_value - expected_value) < 0.01:
            print("✅ 投资组合价值计算正确")
            return True
        else:
            print("❌ 投资组合价值计算不正确")
            return False
        
    except Exception as e:
        print(f"❌ 投资组合价值计算测试失败: {e}")
        return False

def test_integration():
    """集成测试"""
    print("\n" + "=" * 60)
    print("测试 6: 集成测试")
    print("=" * 60)
    
    try:
        # 测试完整的数据流
        from utils.json_serializer import safe_json_dumps, safe_json_loads
        from utils.data_format_validator import ensure_dataframe_format
        from utils.langgraph_state import LangGraphState
        
        # 创建复杂状态
        state = LangGraphState(
            current_date="2025-01-02",
            ticker="AAPL",
            cash=10000.0,
            positions={"AAPL": 100}
        )
        
        # 添加复杂数据
        state.ohlcv_data = {
            "AAPL": pd.DataFrame({
                'Open': [248.93, 243.36],
                'High': [249.1, 244.18],
                'Low': [241.8201, 241.89],
                'Close': [243.85, 243.36],
                'Volume': [55740731, 40244114]
            }, index=pd.date_range('2025-01-02', periods=2))
        }
        
        # 序列化状态
        state_dict = state.to_dict()
        json_str = safe_json_dumps(state_dict)
        print("✅ 状态序列化成功")
        
        # 反序列化状态
        loaded_dict = safe_json_loads(json_str)
        loaded_state = LangGraphState.from_dict(loaded_dict)
        print("✅ 状态反序列化成功")
        
        # 测试投资组合价值计算
        portfolio_value = loaded_state.get_total_portfolio_value()
        print(f"✅ 反序列化后投资组合价值: ${portfolio_value:.2f}")
        
        # 测试智能体输入状态
        agent_input = loaded_state.get_agent_input_state("TAA")
        print("✅ 智能体输入状态获取成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("🚀 开始综合修复验证测试...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("DataFrame布尔值错误修复", test_dataframe_boolean_fix),
        ("增强JSON序列化", test_enhanced_json_serialization),
        ("数据格式验证工具", test_data_format_validator),
        ("增强日志系统", test_enhanced_logging_system),
        ("投资组合价值计算修复", test_portfolio_value_calculation),
        ("集成测试", test_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 打印结果汇总
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有修复验证通过！系统已准备就绪。")
        return True
    else:
        print("⚠️ 部分修复需要进一步调整")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
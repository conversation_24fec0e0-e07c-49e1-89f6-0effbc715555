"""
按天下载新闻数据，支持断点续传和速率限制处理
data_dir_root 是指项目下的 data/ 目录
新闻文件将保存在 data_dir_root/news/ 下
python data/get_news_data.py <TICKER> <START_DATE> <END_DATE>
"""

import requests
import sys
import json
from datetime import datetime, timedelta
import time
import os
import sqlite3 # Import sqlite3
import subprocess

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

from config import ALPHAVANTAGE_API_KEY, DATA_DIR # Import from config

def get_database_path(ticker):
    """
    根据ticker获取对应的数据库路径
    """
    ticker_dir = os.path.join(DATA_DIR, "tickers", ticker.upper())
    os.makedirs(ticker_dir, exist_ok=True)
    return os.path.join(ticker_dir, f"{ticker.upper()}_data.db")

# Alpha Vantage API Base URL
ALPHA_VANTAGE_BASE_URL = "https://www.alphavantage.co/query"

def create_connection(ticker):
    """创建到特定ticker数据库的连接"""
    conn = None
    try:
        database_path = get_database_path(ticker)
        conn = sqlite3.connect(database_path)
        # print(f"Successfully connected to SQLite database: {database_path}", file=sys.stderr) # Suppress frequent connection messages
        return conn
    except sqlite3.Error as e:
        print(f"Database connection error: {e}", file=sys.stderr)
        return None

def insert_news_data(conn, news_articles, ticker):
    """
    Inserts news articles into the database, skipping duplicates.
    Assumes the news table already exists.
    """
    insert_sql = """
    INSERT OR IGNORE INTO news (article_id, ticker, title, url, time_published, authors, summary, banner_image, source, category_within_source, source_domain, topics, overall_sentiment_score, overall_sentiment_label, ticker_sentiment)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
    """ # Use INSERT OR IGNORE to skip duplicates based on PRIMARY KEY (article_id)

    cursor = conn.cursor()
    articles_to_insert = []
    for article in news_articles:
        article_id = article.get('url') # Use url as the unique ID
        if not article_id:
            print(f"Warning: Skipping article with no url: {article.get('title', 'No Title')}", file=sys.stderr)
            continue

        try:
            # Convert list or dict fields to JSON strings
            authors_json = json.dumps(article.get('authors', []))
            topics_json = json.dumps(article.get('topics', []))
            # Ensure ticker_sentiment is a list and convert to JSON string
            ticker_sentiment_data = article.get('ticker_sentiment', [])
            if not isinstance(ticker_sentiment_data, list):
                 print(f"Warning: ticker_sentiment is not a list, skipping for {article_id}. Data: {ticker_sentiment_data}", file=sys.stderr)
                 ticker_sentiment_json = json.dumps([]) # Save JSON of empty list
            else:
                ticker_sentiment_json = json.dumps(ticker_sentiment_data)

            articles_to_insert.append((
                article_id,
                ticker,
                article.get('title'),
                article.get('url'),
                article.get('time_published'),
                authors_json,
                article.get('summary'),
                article.get('banner_image'),
                article.get('source'),
                article.get('category_within_source'),
                article.get('source_domain'),
                topics_json,
                article.get('overall_sentiment_score'),
                article.get('overall_sentiment_label'),
                ticker_sentiment_json
            ))
        except Exception as e:
            print(f"Error processing news article data ({article_id}): {e}", file=sys.stderr)
            continue # Skip this article

    if articles_to_insert:
        try:
            cursor.executemany(insert_sql, articles_to_insert)
            conn.commit()
            print(f"Attempted to insert {len(articles_to_insert)} news articles.", file=sys.stderr)
            # print(f"{cursor.rowcount} new news articles inserted.", file=sys.stderr) # rowcount unreliable
        except sqlite3.Error as e:
            print(f"Error inserting news data into database: {e}", file=sys.stderr)
            # Error during batch insert, might need more specific handling or retry logic
            raise # Re-raise the exception to be caught by the caller
    # else: print("No new articles to insert.") # Can add this if needed

def get_news_in_range(tickers, time_from, time_to, api_key):
    """
    Gets news data for a specific date/time range from Alpha Vantage API.
    Handles API rate limits and errors.
    """
    params = {
        "function": "NEWS_SENTIMENT",
        "tickers": tickers,
        "time_from": time_from,
        "time_to": time_to,
        "apikey": api_key,
        "limit": "1000" # Maximum number of results per call
    }

    print(f"    -> Getting news for {tickers} from {time_from} to {time_to}...", file=sys.stderr)
    # print(f"    -> API request URL: {ALPHA_VANTAGE_BASE_URL}?function=NEWS_SENTIMENT&tickers={tickers}&time_from={time_from}&time_to={time_to}&limit=1000&apikey=***", file=sys.stderr) # Avoid printing API key

    try:
        response = requests.get(ALPHA_VANTAGE_BASE_URL, params=params)
        response.raise_for_status() # Raise HTTPError for bad responses

        data = response.json()

        if "Error Message" in data:
            print(f"    -> Alpha Vantage API Error: {data['Error Message']}", file=sys.stderr)
            return []
        if "Note" in data:
            print(f"    -> Alpha Vantage API Note: {data['Note']}", file=sys.stderr)
            # Free user rate limit
            print("    -> Rate limit reached, waiting 60 seconds before retry...", file=sys.stderr)
            time.sleep(60)
            # Retry the request
            return get_news_in_range(tickers, time_from, time_to, api_key)
        if "Information" in data and "rate limit" in data["Information"].lower():
            print(f"    -> API Limit Reached: {data['Information']}", file=sys.stderr)
            print("    -> Daily API request limit reached. Please try again tomorrow or upgrade your API plan.", file=sys.stderr)
            # Return a special marker indicating API limit
            return "API_LIMIT_REACHED"

        # print(f"    -> API response contains keys: {list(data.keys())}", file=sys.stderr) # Debugging key
        
        if "feed" in data:
            print(f"    -> Fetched {len(data['feed'])} news articles.", file=sys.stderr)
            return data["feed"]
        else:
            print("    -> No news data found in response (feed).", file=sys.stderr)
            # print(f"    -> Response preview: {str(data)[:200]}...", file=sys.stderr) # Debugging response
            return []

    except requests.exceptions.RequestException as e:
        print(f"    -> API request failed: {e}", file=sys.stderr)
        return []
    except json.JSONDecodeError:
        print("    -> Could not parse API response as JSON.", file=sys.stderr)
        # try: print(f"    -> Raw response preview: {response.text[:200]}...", file=sys.stderr) # Debugging raw response
        # except: pass
        return []
    except Exception as e:
        print(f"    -> An unexpected error occurred: {e}", file=sys.stderr)
        return []

def save_news_checkpoint(ticker, next_date_str, end_date_str, data_dir_root):
    """
    Saves a news download checkpoint, recording the next date/month to process.
    Checkpoint file is saved in news_checkpoints/ relative to the project root.
    """
    # Checkpoint directory is in news_checkpoints/ relative to project root
    # data_dir_root is usually project_root/data, so parent is project_root
    checkpoint_base_dir = os.path.dirname(data_dir_root)
    checkpoint_dir = os.path.join(checkpoint_base_dir, "news_checkpoints")
    os.makedirs(checkpoint_dir, exist_ok=True)

    checkpoint_file = os.path.join(checkpoint_dir, f"{ticker}_news_checkpoint.json")
    checkpoint_data = {
        "ticker": ticker,
        "next_date_to_process": next_date_str, # This will be the start date of the next month
        "end_date": end_date_str,
        "data_directory_root": data_dir_root, # Save root data directory for verification
        "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    try:
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
        print(f"News download checkpoint saved to {checkpoint_file}", file=sys.stderr)
    except IOError as e:
        print(f"Failed to save news download checkpoint: {e}", file=sys.stderr)

def load_news_checkpoint(ticker, data_dir_root):
    """
    Loads a news download checkpoint.
    """
    checkpoint_base_dir = os.path.dirname(data_dir_root)
    checkpoint_dir = os.path.join(checkpoint_base_dir, "news_checkpoints")
    checkpoint_file = os.path.join(checkpoint_dir, f"{ticker}_news_checkpoint.json")

    checkpoint_data = None

    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            # Verify the data_directory_root in the checkpoint
            if checkpoint_data.get("data_directory_root") == data_dir_root:
                print(f"Loaded news download checkpoint: {checkpoint_file}", file=sys.stderr)
            else:
                print(f"Warning: Checkpoint file {checkpoint_file} data_directory_root does not match current config. Ignoring checkpoint.", file=sys.stderr)
                checkpoint_data = None # Treat as invalid checkpoint
        except Exception as e:
            print(f"Failed to load news download checkpoint: {e}", file=sys.stderr)
            checkpoint_data = None

    return checkpoint_data


def download_news_data_monthly_and_insert(ticker, start_date_str, end_date_str, api_key, data_dir_root):
    """
    Downloads news data monthly using Alpha Vantage API with checkpointing
    and inserts into the SQLite database.
    """
    conn = create_connection(ticker)
    if conn is None:
        print("Failed to connect to database, cannot download news data.", file=sys.stderr)
        return

    print(f"Starting monthly news download for {ticker} from {start_date_str} to {end_date_str} and inserting into database...")

    # 确保数据库结构已创建
    try:
        prepare_script_path = os.path.join(os.path.dirname(__file__), 'prepare_data.py')
        subprocess.run([sys.executable, prepare_script_path, ticker], check=True)
    except Exception as e:
        print(f"Error ensuring database structure: {e}", file=sys.stderr)
        if conn: conn.close()
        return

    checkpoint_data = load_news_checkpoint(ticker, data_dir_root)

    target_end_date_obj = datetime.strptime(end_date_str, '%Y-%m-%d')

    if checkpoint_data and \
       checkpoint_data.get("ticker") == ticker and \
       checkpoint_data.get("end_date") == end_date_str:
        # Checkpoint saves the start date of the next month to process
        current_month_start = datetime.strptime(checkpoint_data["next_date_to_process"], '%Y-%m-%d')
        print(f"Continuing from news download checkpoint: {ticker} from {current_month_start.strftime('%Y-%m-%d')} to {end_date_str}")
    else:
        current_month_start = datetime.strptime(start_date_str, '%Y-%m-%d')
        print(f"No matching news download checkpoint found or checkpoint expired/mismatch. Starting from scratch: {ticker} from {start_date_str} to {end_date_str}")

    api_limit_reached = False

    # Loop through each month until the start date of the current month exceeds the target end date
    while current_month_start <= target_end_date_obj and not api_limit_reached:
        # Calculate the end date of the current month
        # If it's the month of the target end date, use the target end date
        if current_month_start.year == target_end_date_obj.year and current_month_start.month == target_end_date_obj.month:
             current_month_end = target_end_date_obj
        else:
            # Calculate the first day of the next month, then subtract one day to get the last day of the current month
            next_month = current_month_start.replace(day=28) + timedelta(days=4) # Move to near end of month
            current_month_end = next_month - timedelta(days=next_month.day) # Get last day of current month

        # Format dates for API call (YYYYMMDDTHHMM)
        time_from = current_month_start.strftime('%Y%m%dT0000')
        time_to = current_month_end.strftime('%Y%m%dT2359') # Use 23:59 of the last day of the month

        print(f"Processing news for {ticker} from {current_month_start.strftime('%Y-%m-%d')} to {current_month_end.strftime('%Y-%m-%d')}...", file=sys.stderr)

        # Get news data for the current month range
        news_this_month = get_news_in_range(ticker, time_from, time_to, api_key)

        if news_this_month == "API_LIMIT_REACHED":
            api_limit_reached = True
            # Save checkpoint to the start date of the current month, so it retries this month next time
            save_news_checkpoint(ticker, current_month_start.strftime('%Y-%m-%d'), end_date_str, data_dir_root)
            print(f"API limit reached, checkpoint saved to {current_month_start.strftime('%Y-%m-%d')}. Please run the same command tomorrow to continue.", file=sys.stderr)
            break # Exit the loop

        if isinstance(news_this_month, list) and len(news_this_month) > 0:
            try:
                insert_news_data(conn, news_this_month, ticker)
                print(f"Finished inserting news for {current_month_start.strftime('%Y-%m-%d')} to {current_month_end.strftime('%Y-%m-%d')}.", file=sys.stderr)
            except Exception as e:
                print(f"Error inserting news data for month starting {current_month_start.strftime('%Y-%m-%d')}: {e}", file=sys.stderr)
                # On insertion error, save checkpoint to current month and break to allow inspection/fix
                save_news_checkpoint(ticker, current_month_start.strftime('%Y-%m-%d'), end_date_str, data_dir_root)
                api_limit_reached = True # Treat insertion error similar to API limit for breaking
                break

        elif isinstance(news_this_month, list) and len(news_this_month) == 0:
             print(f"No news data fetched for {current_month_start.strftime('%Y-%m-%d')} to {current_month_end.strftime('%Y-%m-%d')}.", file=sys.stderr)

        # Move to the first day of the next month
        current_month_start = (current_month_start.replace(day=1) + timedelta(days=32)).replace(day=1)
        # Save checkpoint to the start of the next month, whether data was inserted or not
        save_news_checkpoint(ticker, current_month_start.strftime('%Y-%m-%d'), end_date_str, data_dir_root)

        if not api_limit_reached:
             # Add a small delay to be safe, even with monthly calls
             time.sleep(5)

    # If the loop finished normally (processed all months without hitting API limit/insertion error)
    if not api_limit_reached and current_month_start > target_end_date_obj:
         # Clean up checkpoint file
         checkpoint_base_dir = os.path.dirname(data_dir_root)
         checkpoint_dir = os.path.join(checkpoint_base_dir, "news_checkpoints")
         checkpoint_file = os.path.join(checkpoint_dir, f"{ticker}_news_checkpoint.json")
         if os.path.exists(checkpoint_file):
             try:
                 os.remove(checkpoint_file)
                 print(f"News download task completed, checkpoint file {checkpoint_file} cleaned up.", file=sys.stderr)
             except OSError as e:
                 print(f"Failed to clean up checkpoint file {checkpoint_file}: {e}", file=sys.stderr)

    if conn:
        conn.close()
        # print("Database connection closed.", file=sys.stderr) # Suppress frequent messages


def main():
    """Main function"""
    # Check command line arguments
    # Requires 4 arguments: script name, ticker, start_date, end_date
    if len(sys.argv) < 4:
        print("Usage: python data/get_news_data.py <ticker> <start_date> <end_date>", file=sys.stderr)
        print("Example: python data/get_news_data.py AAPL 2023-01-01 2023-12-31", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()
    start_date = sys.argv[2]
    end_date = sys.argv[3]

    # 确保数据目录存在
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "tickers"), exist_ok=True)

    # Download news data monthly and insert into DB
    # Pass DATA_DIR for checkpoint path construction
    if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
        print("\nError: Please configure your Alpha Vantage API key in config.py, skipping news download.", file=sys.stderr)
    else:
        download_news_data_monthly_and_insert(ticker, start_date, end_date, ALPHAVANTAGE_API_KEY, DATA_DIR)

    print(f"\nNews data download and insertion process finished for {ticker} from {start_date} to {end_date}.")


if __name__ == "__main__":
    sys.exit(main()) 
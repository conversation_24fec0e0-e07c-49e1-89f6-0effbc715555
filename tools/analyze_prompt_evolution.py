#!/usr/bin/env python3
"""
提示词演进分析工具

读取周度优化结果文件，生成详细的提示词变化分析报告
"""

import json
import argparse
from typing import Dict, List, Any
from datetime import datetime
import difflib

def load_optimization_results(file_path: str) -> Dict[str, Any]:
    """加载优化结果文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件失败: {e}")
        return {}

def calculate_text_similarity(text1: str, text2: str) -> float:
    """计算两段文本的相似度"""
    matcher = difflib.SequenceMatcher(None, text1, text2)
    return matcher.ratio()

def get_text_diff(text1: str, text2: str) -> List[str]:
    """获取两段文本的差异"""
    diff = difflib.unified_diff(
        text1.splitlines(keepends=True),
        text2.splitlines(keepends=True),
        fromfile='旧提示词',
        tofile='新提示词',
        lineterm=''
    )
    return list(diff)

def analyze_prompt_evolution(results: Dict[str, Any]) -> None:
    """分析提示词演进过程"""
    
    print("="*80)
    print("📋 提示词演进详细分析报告")
    print("="*80)
    
    # 基本信息 - 适应实际JSON结构
    print(f"\n📊 基本信息:")
    print(f"   股票代码: AAPL")  # 从文件名推导
    print(f"   测试期间: 2024-03-01 到 2024-06-01")  # 从数据推导
    print(f"   总周数: {results.get('total_weeks', 0)}")
    print(f"   最终Sharpe ratio: {results.get('final_sharpe_ratio', 0):.4f}")
    print(f"   总收益率: {results.get('total_return', 0)*100:.2f}%")
    
    # 获取周度结果 - 使用optimization_results字段
    weekly_results = results.get('optimization_results', [])
    if not weekly_results:
        print("❌ 未找到周度结果数据")
        return
    
    print(f"\n🔄 逐周提示词变化分析:")
    print("-"*80)
    
    # 分析每一周的变化
    for i, week_result in enumerate(weekly_results):
        week_num = week_result.get('week', i+1)
        end_date = week_result.get('end_date', 'N/A')
        sharpe = week_result.get('cumulative_sharpe', 0)
        prompt = week_result.get('current_prompt', '')
        prompt_length = len(prompt)
        
        print(f"\n第 {week_num} 周 ({end_date}):")
        print(f"   Sharpe ratio: {sharpe:.4f}")
        print(f"   提示词长度: {prompt_length}")
        
        # 与前一周比较
        if i > 0:
            prev_prompt = weekly_results[i-1].get('current_prompt', '')
            prev_length = len(prev_prompt)
            
            # 计算相似度
            similarity = calculate_text_similarity(prev_prompt, prompt)
            
            # 长度变化
            length_change = prompt_length - prev_length
            if length_change > 0:
                print(f"   长度变化: +{length_change} 字符 ✅")
            elif length_change < 0:
                print(f"   长度变化: {length_change} 字符 ✂️")
            else:
                print(f"   长度变化: 无变化 ➡️")
            
            print(f"   文本相似度: {similarity:.3f}")
            
            # 如果变化较大，显示差异
            if similarity < 0.8:
                print(f"   🔍 显著变化检测到，相似度低于0.8")
                
                # 显示关键变化点
                diff_lines = get_text_diff(prev_prompt, prompt)
                changes = [line for line in diff_lines if line.startswith('+++') or line.startswith('---') or line.startswith('+') or line.startswith('-')]
                
                if len(changes) > 6:  # 只显示前几个变化
                    changes = changes[:6] + ['   ... (更多变化)']
                
                for change in changes:
                    if change.startswith('+++') or change.startswith('---'):
                        continue
                    print(f"   {change.rstrip()}")
        
        else:
            print(f"   (初始提示词)")
    
    # 整体统计
    print(f"\n📈 整体演进统计:")
    print("-"*40)
    
    initial_prompt = weekly_results[0].get('current_prompt', '') if weekly_results else ''
    final_prompt = weekly_results[-1].get('current_prompt', '') if weekly_results else ''
    
    initial_length = len(initial_prompt)
    final_length = len(final_prompt)
    total_change = final_length - initial_length
    
    print(f"初始长度: {initial_length}")
    print(f"最终长度: {final_length}")
    print(f"总变化量: {total_change:+d}")
    
    if initial_prompt and final_prompt:
        overall_similarity = calculate_text_similarity(initial_prompt, final_prompt)
        print(f"整体相似度: {overall_similarity:.3f}")
        
        # 计算各周的长度变化趋势
        lengths = [result.get('prompt_length', 0) for result in weekly_results]
        max_length = max(lengths) if lengths else 0
        min_length = min(lengths) if lengths else 0
        
        print(f"最大长度: {max_length}")
        print(f"最小长度: {min_length}")
        print(f"长度波动范围: {max_length - min_length}")
    
    # 性能相关性分析
    print(f"\n🎯 性能与提示词长度相关性:")
    print("-"*40)
    
    # 计算长度与性能的关系
    length_performance_pairs = []
    for result in weekly_results:
        length = result.get('prompt_length', 0)
        sharpe = result.get('cumulative_sharpe', 0)
        length_performance_pairs.append((length, sharpe))
    
    if len(length_performance_pairs) >= 2:
        # 简单的相关性分析
        lengths = [pair[0] for pair in length_performance_pairs]
        performances = [pair[1] for pair in length_performance_pairs]
        
        # 计算Pearson相关系数
        n = len(lengths)
        mean_length = sum(lengths) / n
        mean_perf = sum(performances) / n
        
        numerator = sum((lengths[i] - mean_length) * (performances[i] - mean_perf) for i in range(n))
        denom_length = sum((lengths[i] - mean_length) ** 2 for i in range(n))
        denom_perf = sum((performances[i] - mean_perf) ** 2 for i in range(n))
        
        if denom_length > 0 and denom_perf > 0:
            correlation = numerator / (denom_length * denom_perf) ** 0.5
            print(f"长度-性能相关系数: {correlation:.3f}")
            
            if correlation > 0.3:
                print("✅ 提示词长度与性能呈正相关")
            elif correlation < -0.3:
                print("❌ 提示词长度与性能呈负相关")
            else:
                print("➡️ 提示词长度与性能相关性较弱")

def save_detailed_prompt_history(results: Dict[str, Any], output_file: str) -> None:
    """保存详细的提示词历史到文件"""
    weekly_results = results.get('optimization_results', [])
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("提示词演进历史记录\n")
        f.write("="*80 + "\n\n")
        
        for i, week_result in enumerate(weekly_results):
            week_num = week_result.get('week', i+1)
            end_date = week_result.get('end_date', 'N/A')
            sharpe = week_result.get('cumulative_sharpe', 0)
            prompt = week_result.get('current_prompt', '')
            
            f.write(f"第 {week_num} 周 ({end_date}) - Sharpe: {sharpe:.4f}\n")
            f.write("-" * 60 + "\n")
            f.write(prompt)
            f.write("\n\n" + "="*80 + "\n\n")
    
    print(f"📁 详细提示词历史已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="分析提示词演进过程")
    parser.add_argument("--file", "-f", required=True, help="优化结果JSON文件路径")
    parser.add_argument("--save-history", "-s", help="保存详细提示词历史的输出文件路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细信息")
    
    args = parser.parse_args()
    
    # 加载结果
    results = load_optimization_results(args.file)
    if not results:
        return
    
    # 分析演进过程
    analyze_prompt_evolution(results)
    
    # 保存详细历史（如果指定）
    if args.save_history:
        save_detailed_prompt_history(results, args.save_history)

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
"""
Myerson-OPRO 优化有效性评估框架
用于量化评估优化过程是否正常工作
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Any
import json
from datetime import datetime

class OptimizationEvaluator:
    """优化过程有效性评估器"""
    
    def __init__(self):
        self.optimization_history = []
        self.effectiveness_scores = {}
        
    def add_optimization_step(self, step: int, myerson_value: float, 
                            myerson_reward: float, prompt_length: int,
                            trading_actions: List[str] = None):
        """添加优化步骤数据"""
        step_data = {
            'step': step,
            'myerson_value': myerson_value,
            'myerson_reward': myerson_reward,
            'prompt_length': prompt_length,
            'trading_actions': trading_actions or [],
            'timestamp': datetime.now().isoformat()
        }
        self.optimization_history.append(step_data)
        
    def evaluate_data_quality(self, ohlcv_data) -> Dict[str, Any]:
        """评估数据质量"""
        if ohlcv_data.empty:
            return {
                'score': 0.0,
                'issues': ['数据为空'],
                'recommendations': ['检查数据库连接和日期范围']
            }
            
        data_points = len(ohlcv_data)
        price_volatility = ohlcv_data['Close'].std() / ohlcv_data['Close'].mean()
        price_range = (ohlcv_data['Close'].max() - ohlcv_data['Close'].min()) / ohlcv_data['Close'].mean()
        
        score = 0.0
        issues = []
        recommendations = []
        
        # 数据点数量评分 (40分)
        if data_points >= 7:
            score += 40
        elif data_points >= 5:
            score += 30
            issues.append(f'数据点较少: {data_points}天')
            recommendations.append('增加训练天数到7天以上')
        else:
            score += 10
            issues.append(f'数据点严重不足: {data_points}天')
            recommendations.append('至少需要5-7天的交易数据')
            
        # 价格波动性评分 (30分)
        if price_volatility > 0.02:  # 2%以上波动
            score += 30
        elif price_volatility > 0.01:  # 1-2%波动
            score += 20
            issues.append(f'价格波动较小: {price_volatility:.3f}')
        else:
            score += 5
            issues.append(f'价格几乎无波动: {price_volatility:.3f}')
            recommendations.append('选择波动更大的时间段或股票')
            
        # 价格范围评分 (30分)
        if price_range > 0.05:  # 5%以上价格范围
            score += 30
        elif price_range > 0.02:  # 2-5%价格范围
            score += 20
        else:
            score += 5
            issues.append(f'价格范围太小: {price_range:.3f}')
            
        return {
            'score': score / 100.0,  # 归一化到0-1
            'data_points': data_points,
            'volatility': price_volatility,
            'price_range': price_range,
            'issues': issues,
            'recommendations': recommendations
        }
        
    def evaluate_signal_effectiveness(self) -> Dict[str, Any]:
        """评估信号有效性"""
        if len(self.optimization_history) < 2:
            return {
                'score': 0.0,
                'issues': ['优化历史不足'],
                'recommendations': ['运行更多优化步骤']
            }
            
        myerson_values = [step['myerson_value'] for step in self.optimization_history]
        myerson_rewards = [step['myerson_reward'] for step in self.optimization_history]
        
        score = 0.0
        issues = []
        recommendations = []
        
        # 检查Myerson值变化 (50分)
        value_variance = np.var(myerson_values)
        if value_variance > 0.001:  # 有显著变化
            score += 50
        elif value_variance > 0.0001:  # 有一些变化
            score += 25
            issues.append(f'Myerson值变化较小: {value_variance:.6f}')
        else:
            score += 0
            issues.append('Myerson值无变化或全为0')
            recommendations.append('检查交易模拟逻辑是否正确工作')
            
        # 检查奖励信号 (30分)
        reward_variance = np.var(myerson_rewards)
        if reward_variance > 0.001:
            score += 30
        elif reward_variance > 0.0001:
            score += 15
        else:
            issues.append('奖励信号无变化')
            
        # 检查趋势 (20分)
        if len(myerson_values) >= 3:
            # 简单的趋势检测
            early_avg = np.mean(myerson_values[:len(myerson_values)//2])
            late_avg = np.mean(myerson_values[len(myerson_values)//2:])
            if late_avg > early_avg:
                score += 20  # 有改善趋势
            elif late_avg < early_avg * 0.9:  # 显著下降
                score += 10
                issues.append('性能呈下降趋势')
            else:
                score += 5  # 平稳
                
        return {
            'score': score / 100.0,
            'value_variance': value_variance,
            'reward_variance': reward_variance,
            'issues': issues,
            'recommendations': recommendations
        }
        
    def evaluate_optimization_behavior(self) -> Dict[str, Any]:
        """评估优化行为"""
        if len(self.optimization_history) < 2:
            return {
                'score': 0.0,
                'issues': ['优化历史不足'],
                'recommendations': ['运行更多优化步骤']
            }
            
        prompt_lengths = [step['prompt_length'] for step in self.optimization_history]
        
        score = 0.0
        issues = []
        recommendations = []
        
        # 检查提示词变化 (40分)
        length_variance = np.var(prompt_lengths)
        if length_variance > 100:  # 提示词长度有变化
            score += 40
        elif length_variance > 10:
            score += 20
            issues.append('提示词变化较小')
        else:
            score += 5
            issues.append('提示词几乎无变化')
            recommendations.append('检查提示词生成逻辑')
            
        # 检查交易行为多样性 (60分)
        all_actions = []
        for step in self.optimization_history:
            all_actions.extend(step.get('trading_actions', []))
            
        if all_actions:
            unique_actions = set(all_actions)
            action_diversity = len(unique_actions) / max(len(all_actions), 1)
            
            if action_diversity > 0.5:  # 高多样性
                score += 60
            elif action_diversity > 0.2:  # 中等多样性
                score += 30
                issues.append(f'交易决策多样性中等: {action_diversity:.2f}')
            else:
                score += 10
                issues.append(f'交易决策缺乏多样性: {action_diversity:.2f}')
                recommendations.append('优化提示词以产生更多样的交易决策')
        else:
            issues.append('无交易行为记录')
            
        return {
            'score': score / 100.0,
            'prompt_length_variance': length_variance,
            'action_diversity': len(set(all_actions)) / max(len(all_actions), 1) if all_actions else 0,
            'issues': issues,
            'recommendations': recommendations
        }
        
    def calculate_overall_effectiveness(self, data_quality_result: Dict[str, Any],
                                      signal_effectiveness_result: Dict[str, Any],
                                      optimization_behavior_result: Dict[str, Any]) -> Dict[str, Any]:
        """计算总体有效性评分"""
        
        # 权重分配
        weights = {
            'data_quality': 0.4,        # 数据质量最重要
            'signal_effectiveness': 0.4, # 信号有效性同等重要  
            'optimization_behavior': 0.2  # 优化行为相对次要
        }
        
        overall_score = (
            data_quality_result['score'] * weights['data_quality'] +
            signal_effectiveness_result['score'] * weights['signal_effectiveness'] +
            optimization_behavior_result['score'] * weights['optimization_behavior']
        )
        
        # 综合问题和建议
        all_issues = (
            data_quality_result.get('issues', []) +
            signal_effectiveness_result.get('issues', []) +
            optimization_behavior_result.get('issues', [])
        )
        
        all_recommendations = (
            data_quality_result.get('recommendations', []) +
            signal_effectiveness_result.get('recommendations', []) +
            optimization_behavior_result.get('recommendations', [])
        )
        
        # 效果等级判定
        if overall_score >= 0.8:
            effectiveness_level = "🟢 优秀"
        elif overall_score >= 0.6:
            effectiveness_level = "🟡 良好"
        elif overall_score >= 0.4:
            effectiveness_level = "🟠 一般"
        else:
            effectiveness_level = "🔴 差"
            
        return {
            'overall_score': overall_score,
            'effectiveness_level': effectiveness_level,
            'component_scores': {
                'data_quality': data_quality_result['score'],
                'signal_effectiveness': signal_effectiveness_result['score'],
                'optimization_behavior': optimization_behavior_result['score']
            },
            'weights': weights,
            'issues': list(set(all_issues)),  # 去重
            'recommendations': list(set(all_recommendations))  # 去重
        }
        
    def generate_report(self, ohlcv_data) -> str:
        """生成详细的评估报告"""
        
        # 执行各项评估
        data_quality = self.evaluate_data_quality(ohlcv_data)
        signal_effectiveness = self.evaluate_signal_effectiveness()
        optimization_behavior = self.evaluate_optimization_behavior()
        overall_result = self.calculate_overall_effectiveness(
            data_quality, signal_effectiveness, optimization_behavior
        )
        
        # 生成报告
        report = []
        report.append("📋 Myerson-OPRO 优化有效性评估报告")
        report.append("=" * 60)
        
        # 总体评估
        report.append(f"\n🎯 总体有效性: {overall_result['effectiveness_level']}")
        report.append(f"📊 综合评分: {overall_result['overall_score']:.2f}/1.00")
        
        # 分项评分
        report.append(f"\n📈 分项评分:")
        for component, score in overall_result['component_scores'].items():
            weight = overall_result['weights'][component]
            report.append(f"  • {component}: {score:.2f} (权重: {weight:.1f})")
            
        # 数据质量详情
        report.append(f"\n📊 数据质量评估:")
        report.append(f"  评分: {data_quality['score']:.2f}")
        if 'data_points' in data_quality:
            report.append(f"  数据点: {data_quality['data_points']}天")
            report.append(f"  价格波动: {data_quality['volatility']:.4f}")
            report.append(f"  价格范围: {data_quality['price_range']:.4f}")
            
        # 信号有效性详情
        report.append(f"\n📡 信号有效性评估:")
        report.append(f"  评分: {signal_effectiveness['score']:.2f}")
        if 'value_variance' in signal_effectiveness:
            report.append(f"  Myerson值方差: {signal_effectiveness['value_variance']:.6f}")
            report.append(f"  奖励信号方差: {signal_effectiveness['reward_variance']:.6f}")
            
        # 优化行为详情
        report.append(f"\n🔄 优化行为评估:")
        report.append(f"  评分: {optimization_behavior['score']:.2f}")
        if 'prompt_length_variance' in optimization_behavior:
            report.append(f"  提示词长度方差: {optimization_behavior['prompt_length_variance']:.2f}")
            report.append(f"  交易决策多样性: {optimization_behavior['action_diversity']:.2f}")
            
        # 问题列表
        if overall_result['issues']:
            report.append(f"\n⚠️ 发现的问题:")
            for issue in overall_result['issues']:
                report.append(f"  • {issue}")
                
        # 改进建议
        if overall_result['recommendations']:
            report.append(f"\n💡 改进建议:")
            for rec in overall_result['recommendations']:
                report.append(f"  • {rec}")
                
        # 历史数据摘要
        if self.optimization_history:
            report.append(f"\n📈 优化历史摘要:")
            report.append(f"  优化步数: {len(self.optimization_history)}")
            myerson_values = [step['myerson_value'] for step in self.optimization_history]
            report.append(f"  Myerson值范围: {min(myerson_values):.6f} ~ {max(myerson_values):.6f}")
            
        return "\n".join(report)
        
    def save_results(self, filename: str):
        """保存评估结果"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'optimization_history': self.optimization_history,
            'effectiveness_scores': self.effectiveness_scores
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

def demo_effectiveness_evaluation():
    """演示有效性评估"""
    print("🔍 优化有效性评估演示")
    print("=" * 40)
    
    # 创建评估器
    evaluator = OptimizationEvaluator()
    
    # 模拟一些优化步骤数据
    steps_data = [
        (1, 0.0, 0.0, 300, ['HOLD', 'HOLD', 'HOLD']),
        (2, 0.0, 0.0, 315, ['HOLD', 'HOLD', 'HOLD']),
        (3, 0.0, 0.0, 298, ['HOLD', 'HOLD', 'HOLD']),
    ]
    
    for step, mv, mr, pl, actions in steps_data:
        evaluator.add_optimization_step(step, mv, mr, pl, actions)
    
    # 创建模拟数据
    import pandas as pd
    dates = pd.date_range('2025-05-13', '2025-05-14', freq='D')
    mock_data = pd.DataFrame({
        'Close': [200.0, 200.1],  # 几乎无变化的价格
        'Open': [199.8, 200.05],
        'High': [200.2, 200.3],
        'Low': [199.5, 199.9],
        'Volume': [1000000, 1100000]
    }, index=dates)
    
    # 生成报告
    report = evaluator.generate_report(mock_data)
    print(report)

if __name__ == "__main__":
    demo_effectiveness_evaluation() 
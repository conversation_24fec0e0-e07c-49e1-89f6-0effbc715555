#!/usr/bin/env python3
"""
增强的数据管理器
实现智能缓存和批量数据下载策略
"""

import os
import sys
import json
import sqlite3
import subprocess
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import argparse

class EnhancedDataManager:
    """增强的数据管理器，支持智能缓存和批量下载"""
    
    def __init__(self, force_refresh: bool = False, verbose: bool = True):
        """
        初始化数据管理器
        
        Args:
            force_refresh: 是否强制刷新所有数据
            verbose: 是否显示详细日志
        """
        self.force_refresh = force_refresh
        self.verbose = verbose
        self.cache_dir = "data/cache"
        os.makedirs(self.cache_dir, exist_ok=True)
        
    def log(self, message: str) -> None:
        """记录日志"""
        if self.verbose:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def get_cache_info(self, ticker: str) -> Dict[str, Any]:
        """获取缓存信息"""
        cache_file = os.path.join(self.cache_dir, f"{ticker}_cache_info.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "ticker": ticker,
            "ohlcv_range": {"start": None, "end": None},
            "news_range": {"start": None, "end": None},
            "fundamental_last_update": None,
            "last_cache_update": None
        }
    
    def update_cache_info(self, ticker: str, cache_info: Dict[str, Any]) -> None:
        """更新缓存信息"""
        cache_info["last_cache_update"] = datetime.now().isoformat()
        cache_file = os.path.join(self.cache_dir, f"{ticker}_cache_info.json")
        
        with open(cache_file, 'w') as f:
            json.dump(cache_info, f, indent=2)
    
    def get_database_ranges(self, ticker: str) -> Dict[str, Tuple[Optional[str], Optional[str]]]:
        """获取数据库中的数据范围"""
        db_path = f"data/tickers/{ticker}/{ticker}_data.db"
        
        if not os.path.exists(db_path):
            return {
                "ohlcv": (None, None),
                "news": (None, None)
            }
        
        ranges = {"ohlcv": (None, None), "news": (None, None)}
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查OHLCV数据范围
            cursor.execute("SELECT MIN(date), MAX(date) FROM ohlcv WHERE ticker = ?", (ticker,))
            ohlcv_range = cursor.fetchone()
            if ohlcv_range and ohlcv_range[0]:
                ranges["ohlcv"] = ohlcv_range
            
            # 检查新闻数据范围
            cursor.execute("SELECT MIN(date), MAX(date) FROM news WHERE ticker = ?", (ticker,))
            news_range = cursor.fetchone()
            if news_range and news_range[0]:
                ranges["news"] = news_range
            
            conn.close()
            
        except Exception as e:
            self.log(f"⚠️  Error checking database ranges: {e}")
        
        return ranges
    
    def download_comprehensive_data(self, ticker: str, target_start_date: str = "2020-01-01") -> bool:
        """
        下载综合数据集
        
        Args:
            ticker: 股票代码
            target_start_date: 目标开始日期
            
        Returns:
            是否成功
        """
        self.log(f"🚀 开始为 {ticker} 下载综合数据集...")
        
        # 获取当前缓存信息
        cache_info = self.get_cache_info(ticker)
        db_ranges = self.get_database_ranges(ticker)
        
        success = True
        
        # 1. 下载OHLCV数据
        success &= self._download_ohlcv_data(ticker, target_start_date, cache_info, db_ranges)
        
        # 2. 下载新闻数据
        success &= self._download_news_data(ticker, target_start_date, cache_info, db_ranges)
        
        # 3. 下载基本面数据
        success &= self._download_fundamental_data(ticker, cache_info)
        
        # 更新缓存信息
        self.update_cache_info(ticker, cache_info)
        
        if success:
            self.log(f"✅ {ticker} 综合数据下载完成")
        else:
            self.log(f"⚠️  {ticker} 数据下载部分失败")
        
        return success
    
    def _download_ohlcv_data(self, ticker: str, target_start_date: str, 
                           cache_info: Dict[str, Any], db_ranges: Dict) -> bool:
        """下载OHLCV数据"""
        current_end = datetime.now().strftime("%Y-%m-%d")
        db_start, db_end = db_ranges["ohlcv"]
        
        # 检查是否需要下载
        if not self.force_refresh and db_start and db_end:
            # 检查数据是否足够新（最近7天内）
            try:
                db_end_date = datetime.strptime(db_end, "%Y-%m-%d")
                days_old = (datetime.now() - db_end_date).days
                
                if days_old <= 7:
                    self.log(f"📊 OHLCV数据已是最新 (最后更新: {db_end})")
                    cache_info["ohlcv_range"] = {"start": db_start, "end": db_end}
                    return True
            except:
                pass
        
        # 下载数据
        self.log(f"📊 下载OHLCV数据: {target_start_date} 到 {current_end}")
        
        try:
            cmd = [
                sys.executable, "data/get_OHLCV_data.py",
                ticker, target_start_date, current_end
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.log(f"✅ OHLCV数据下载成功")
                cache_info["ohlcv_range"] = {"start": target_start_date, "end": current_end}
                return True
            else:
                self.log(f"❌ OHLCV数据下载失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.log(f"❌ OHLCV数据下载异常: {e}")
            return False
    
    def _download_news_data(self, ticker: str, target_start_date: str,
                          cache_info: Dict[str, Any], db_ranges: Dict) -> bool:
        """下载新闻数据"""
        current_end = datetime.now().strftime("%Y-%m-%d")
        db_start, db_end = db_ranges["news"]
        
        # 检查是否需要下载
        if not self.force_refresh and db_start and db_end:
            try:
                db_end_date = datetime.strptime(db_end, "%Y-%m-%d")
                days_old = (datetime.now() - db_end_date).days
                
                if days_old <= 7:
                    self.log(f"📰 新闻数据已是最新 (最后更新: {db_end})")
                    cache_info["news_range"] = {"start": db_start, "end": db_end}
                    return True
            except:
                pass
        
        # 下载数据（考虑API限制，使用较短的时间范围）
        # 如果没有现有数据，从较近的日期开始
        if not db_start:
            # 如果没有历史数据，从6个月前开始
            start_date = (datetime.now() - timedelta(days=180)).strftime("%Y-%m-%d")
        else:
            start_date = target_start_date
        
        self.log(f"📰 下载新闻数据: {start_date} 到 {current_end}")
        
        try:
            cmd = [
                sys.executable, "data/get_news_data.py",
                ticker, start_date, current_end
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                self.log(f"✅ 新闻数据下载成功")
                cache_info["news_range"] = {"start": start_date, "end": current_end}
                return True
            else:
                # 新闻数据可能因为API限制失败，这是可以接受的
                self.log(f"⚠️  新闻数据下载受限: {result.stderr}")
                return True  # 不算作失败
                
        except Exception as e:
            self.log(f"⚠️  新闻数据下载异常: {e}")
            return True  # 不算作失败
    
    def _download_fundamental_data(self, ticker: str, cache_info: Dict[str, Any]) -> bool:
        """下载基本面数据"""
        # 检查是否需要下载
        last_update = cache_info.get("fundamental_last_update")
        if not self.force_refresh and last_update:
            try:
                last_update_date = datetime.fromisoformat(last_update)
                days_old = (datetime.now() - last_update_date).days
                
                if days_old <= 30:  # 30天内更新过
                    self.log(f"📈 基本面数据已是最新 (最后更新: {last_update})")
                    return True
            except:
                pass
        
        self.log(f"📈 下载基本面数据")
        
        try:
            cmd = [sys.executable, "data/get_fundamental_data.py", ticker]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.log(f"✅ 基本面数据下载成功")
                cache_info["fundamental_last_update"] = datetime.now().isoformat()
                return True
            else:
                self.log(f"❌ 基本面数据下载失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.log(f"❌ 基本面数据下载异常: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强的数据管理器")
    parser.add_argument("ticker", help="股票代码")
    parser.add_argument("--start-date", default="2020-01-01", help="开始日期")
    parser.add_argument("--force-refresh", action="store_true", help="强制刷新所有数据")
    parser.add_argument("--quiet", action="store_true", help="静默模式")
    
    args = parser.parse_args()
    
    manager = EnhancedDataManager(
        force_refresh=args.force_refresh,
        verbose=not args.quiet
    )
    
    success = manager.download_comprehensive_data(args.ticker, args.start_date)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()

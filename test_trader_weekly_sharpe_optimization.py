#!/usr/bin/env python3
"""
单交易员智能体周度Sharpe Ratio优化测试

基于周度Sharpe ratio进行提示词优化，每天交易决策基于：
- 最近一期年报和季报
- 前30天OHLCV数据
- 前7天新闻数据
测试期间：2024年3月1日 - 2024年6月1日
"""

import os
import sys
import json
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple
import argparse

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config import MODEL_CONFIGS, DEFAULT_MODEL
from utils.model_interface import ModelInterface
from utils.myerson_opro_optimizer import MyersonOPROOptimizer

class WeeklyTraderOptimizer:
    """周度交易员优化器"""
    
    def __init__(self, ticker: str = "AAPL", verbose: bool = True):
        self.ticker = ticker
        self.verbose = verbose
        self.start_date = "2024-03-01"
        self.end_date = "2024-06-01"
        
        # 初始化模型接口
        self.model_interface = ModelInterface(
            model_name=DEFAULT_MODEL,
            config=MODEL_CONFIGS[DEFAULT_MODEL]
        )
        
        # 数据库路径
        self.db_path = f"data/tickers/{ticker}/{ticker}_data.db"
        
        # 交易记录
        self.trading_history = []
        self.weekly_returns = []
        self.weekly_sharpe_ratios = []
        
        # 初始化交易员提示词
        self.trader_prompt = """你是一个专业的股票交易员。基于提供的数据，你需要做出交易决策。

分析以下信息：
1. 最新财务报表数据（年报和季报）
2. 过去30天的OHLCV价格数据
3. 过去7天的相关新闻

请根据这些信息做出交易决策：BUY（买入）、SELL（卖出）或HOLD（持有）。

请以JSON格式返回你的决策：
[
    "decision": "BUY/SELL/HOLD",
    "confidence": 0.8,
    "reasoning": "详细的分析理由"
]

数据：
{data}
"""
        
        # 初始化优化器
        self.optimizer = MyersonOPROOptimizer(
            agent_id="TRA",
            agent_type="TRA", 
            model_interface=self.model_interface,
            max_optimization_steps=10,
            history_prompts_k=3,
            max_num_generated_instructions=2,
            plateau_patience=3
        )
        
        if self.verbose:
            print(f"初始化周度交易员优化器")
            print(f"股票代码: {ticker}")
            print(f"测试期间: {self.start_date} 到 {self.end_date}")
            print(f"数据库路径: {self.db_path}")
    
    def get_trading_dates(self) -> List[str]:
        """获取交易日期列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            query = """
            SELECT DISTINCT trade_date 
            FROM ohlcv 
            WHERE ticker = ? AND trade_date BETWEEN ? AND ?
            ORDER BY trade_date
            """
            df = pd.read_sql_query(query, conn, params=[self.ticker, self.start_date, self.end_date])
            conn.close()
            return df['trade_date'].tolist()
        except Exception as e:
            print(f"获取交易日期失败: {e}")
            return []
    
    def get_latest_financial_data(self, date: str) -> Dict[str, Any]:
        """获取指定日期前最新的财务数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取最新年报
            annual_query = """
            SELECT * FROM annual_financials 
            WHERE ticker = ? AND fiscal_date <= ?
            ORDER BY fiscal_date DESC 
            LIMIT 1
            """
            annual_df = pd.read_sql_query(annual_query, conn, params=[self.ticker, date])
            
            # 获取最新季报
            quarterly_query = """
            SELECT * FROM quarterly_financials 
            WHERE ticker = ? AND fiscal_date <= ?
            ORDER BY fiscal_date DESC 
            LIMIT 1
            """
            quarterly_df = pd.read_sql_query(quarterly_query, conn, params=[self.ticker, date])
            
            conn.close()
            
            return {
                "annual": annual_df.to_dict('records')[0] if not annual_df.empty else {},
                "quarterly": quarterly_df.to_dict('records')[0] if not quarterly_df.empty else {}
            }
        except Exception as e:
            print(f"获取财务数据失败: {e}")
            return {"annual": {}, "quarterly": {}}
    
    def get_ohlcv_data(self, date: str, days: int = 30) -> pd.DataFrame:
        """获取指定日期前N天的OHLCV数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            end_date = datetime.strptime(date, "%Y-%m-%d")
            start_date = end_date - timedelta(days=days)
            
            query = """
            SELECT * FROM ohlcv 
            WHERE ticker = ? AND trade_date BETWEEN ? AND ?
            ORDER BY trade_date DESC
            LIMIT ?
            """
            df = pd.read_sql_query(query, conn, params=[
                self.ticker, 
                start_date.strftime("%Y-%m-%d"), 
                date, 
                days
            ])
            conn.close()
            return df
        except Exception as e:
            print(f"获取OHLCV数据失败: {e}")
            return pd.DataFrame()
    
    def get_news_data(self, date: str, days: int = 7) -> List[Dict]:
        """获取指定日期前N天的新闻数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            end_date = datetime.strptime(date, "%Y-%m-%d")
            start_date = end_date - timedelta(days=days)
            
            query = """
            SELECT title, summary, time_published, overall_sentiment_score, overall_sentiment_label
            FROM news 
            WHERE ticker_sentiment LIKE ? 
            AND time_published BETWEEN ? AND ?
            ORDER BY time_published DESC
            LIMIT 20
            """
            
            start_time = start_date.strftime("%Y%m%dT000000")
            end_time = end_date.strftime("%Y%m%dT235959")
            
            df = pd.read_sql_query(query, conn, params=[
                f'%{self.ticker}%',
                start_time,
                end_time
            ])
            conn.close()
            return df.to_dict('records')
        except Exception as e:
            print(f"获取新闻数据失败: {e}")
            return []
    
    def format_data_for_prompt(self, financial_data: Dict, ohlcv_data: pd.DataFrame, news_data: List[Dict]) -> str:
        """格式化数据用于提示词"""
        data_str = f"股票代码: {self.ticker}\n\n"
        
        # 财务数据
        if financial_data["annual"]:
            data_str += "最新年报数据:\n"
            annual = financial_data["annual"]
            data_str += f"- 财年结束日期: {annual.get('fiscal_date', 'N/A')}\n"
            data_str += f"- 报告类型: {annual.get('report_type', 'N/A')}\n"
            # 尝试解析JSON数据
            try:
                import json
                annual_data = json.loads(annual.get('data_json', '{}'))
                data_str += f"- 财务数据: {str(annual_data)[:200]}...\n"
            except:
                data_str += f"- 财务数据: 解析失败\n"
            data_str += "\n"
        
        if financial_data["quarterly"]:
            data_str += "最新季报数据:\n"
            quarterly = financial_data["quarterly"]
            data_str += f"- 财年结束日期: {quarterly.get('fiscal_date', 'N/A')}\n"
            data_str += f"- 报告类型: {quarterly.get('report_type', 'N/A')}\n"
            # 尝试解析JSON数据
            try:
                import json
                quarterly_data = json.loads(quarterly.get('data_json', '{}'))
                data_str += f"- 财务数据: {str(quarterly_data)[:200]}...\n"
            except:
                data_str += f"- 财务数据: 解析失败\n"
            data_str += "\n"
        
        # OHLCV数据
        if not ohlcv_data.empty:
            data_str += f"过去30天OHLCV数据 (共{len(ohlcv_data)}天):\n"
            data_str += f"- 最新收盘价: ${ohlcv_data.iloc[0]['Close']:.2f}\n"
            data_str += f"- 30天最高价: ${ohlcv_data['High'].max():.2f}\n"
            data_str += f"- 30天最低价: ${ohlcv_data['Low'].min():.2f}\n"
            data_str += f"- 30天平均成交量: {ohlcv_data['Volume'].mean():.0f}\n"
            
            # 计算技术指标
            if len(ohlcv_data) >= 20:
                sma_20 = ohlcv_data['Close'].head(20).mean()
                data_str += f"- 20日移动平均: ${sma_20:.2f}\n"
            
            data_str += "\n"
        
        # 新闻数据
        if news_data:
            data_str += f"过去7天新闻 (共{len(news_data)}条):\n"
            for i, news in enumerate(news_data[:5]):  # 只显示前5条
                data_str += f"{i+1}. {news.get('title', 'N/A')}\n"
                data_str += f"   情感分数: {news.get('overall_sentiment_score', 'N/A')}\n"
                data_str += f"   情感标签: {news.get('overall_sentiment_label', 'N/A')}\n"
                if news.get('summary'):
                    data_str += f"   摘要: {news['summary'][:100]}...\n"
                data_str += "\n"
        
        return data_str
    
    def make_trading_decision(self, date: str) -> Dict[str, Any]:
        """做出交易决策"""
        # 获取数据
        financial_data = self.get_latest_financial_data(date)
        ohlcv_data = self.get_ohlcv_data(date, 30)
        news_data = self.get_news_data(date, 7)
        
        # 格式化数据
        formatted_data = self.format_data_for_prompt(financial_data, ohlcv_data, news_data)
        
        # 构建完整提示词 - 安全地替换数据，避免格式化冲突
        full_prompt = self.trader_prompt.replace("{data}", formatted_data)
        
        try:
            # 调用LLM
            response = self.model_interface.get_completion(full_prompt)
            
            # 解析响应
            try:
                decision_data = json.loads(response)
                decision = decision_data.get("decision", "HOLD").upper()
                confidence = decision_data.get("confidence", 0.5)
                reasoning = decision_data.get("reasoning", "无具体理由")
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试从文本中提取决策
                response_upper = response.upper()
                if "BUY" in response_upper:
                    decision = "BUY"
                elif "SELL" in response_upper:
                    decision = "SELL"
                else:
                    decision = "HOLD"
                confidence = 0.5
                reasoning = "JSON解析失败，从文本提取决策"
            
            return {
                "date": date,
                "decision": decision,
                "confidence": confidence,
                "reasoning": reasoning,
                "response": response
            }
            
        except Exception as e:
            print(f"交易决策失败 {date}: {e}")
            return {
                "date": date,
                "decision": "HOLD",
                "confidence": 0.0,
                "reasoning": f"决策失败: {str(e)}",
                "response": ""
            }
    
    def calculate_weekly_returns_and_daily_data(self, trading_dates: List[str]) -> List[Tuple[str, float, List[float]]]:
        """计算周度数据，包括每日收益率"""
        weekly_data = []
        
        # 按周分组交易日期
        weeks = []
        current_week = []
        
        for date in trading_dates:
            date_obj = datetime.strptime(date, "%Y-%m-%d")
            if not current_week:
                current_week = [date]
            else:
                last_date = datetime.strptime(current_week[-1], "%Y-%m-%d")
                # 如果是同一周（周一到周日）
                if (date_obj - last_date).days <= 7 and date_obj.isocalendar()[1] == last_date.isocalendar()[1]:
                    current_week.append(date)
                else:
                    if current_week:
                        weeks.append(current_week)
                    current_week = [date]
        
        if current_week:
            weeks.append(current_week)
        
        # 计算每周的数据
        for week_dates in weeks:
            if len(week_dates) < 2:
                continue
                
            week_start = week_dates[0]
            week_end = week_dates[-1]
            
            try:
                conn = sqlite3.connect(self.db_path)
                
                # 获取这一周所有交易日的收盘价
                week_prices_query = """
                SELECT trade_date, Close FROM ohlcv 
                WHERE ticker = ? AND trade_date >= ? AND trade_date <= ?
                ORDER BY trade_date
                """
                week_prices = pd.read_sql_query(week_prices_query, conn, params=[self.ticker, week_start, week_end])
                
                conn.close()
                
                if len(week_prices) >= 2:
                    # 计算每日收益率
                    daily_returns = []
                    for i in range(1, len(week_prices)):
                        prev_price = week_prices.iloc[i-1]['Close']
                        curr_price = week_prices.iloc[i]['Close']
                        daily_return = (curr_price - prev_price) / prev_price
                        daily_returns.append(daily_return)
                    
                    # 计算周总收益率（复合收益率）
                    weekly_return = (week_prices.iloc[-1]['Close'] - week_prices.iloc[0]['Close']) / week_prices.iloc[0]['Close']
                    
                    weekly_data.append((week_end, weekly_return, daily_returns))
                    
            except Exception as e:
                print(f"计算周数据失败 {week_start}-{week_end}: {e}")
        
        return weekly_data
    
    def calculate_sharpe_ratio_from_daily_returns(self, all_daily_returns: List[float], risk_free_rate: float = 0.02) -> float:
        """基于每日收益率计算Sharpe ratio"""
        if len(all_daily_returns) < 2:
            return 0.0
        
        returns_array = np.array(all_daily_returns)
        daily_risk_free_rate = risk_free_rate / 252  # 年化无风险收益率转换为日度
        excess_returns = returns_array - daily_risk_free_rate
        
        if np.std(excess_returns) == 0:
            return 0.0
        
        # 计算年化Sharpe ratio
        daily_sharpe = np.mean(excess_returns) / np.std(excess_returns)
        annualized_sharpe = daily_sharpe * np.sqrt(252)  # 年化
        
        return annualized_sharpe
    
    def run_optimization(self, max_weeks: int = 12) -> Dict[str, Any]:
        """运行优化过程"""
        if self.verbose:
            print(f"\n开始运行周度Sharpe ratio优化...")
        
        # 获取交易日期
        trading_dates = self.get_trading_dates()
        if not trading_dates:
            print("未找到交易日期数据")
            return {}
        
        if self.verbose:
            print(f"找到 {len(trading_dates)} 个交易日")
        
        # 计算周度数据
        weekly_data = self.calculate_weekly_returns_and_daily_data(trading_dates)
        if self.verbose:
            print(f"计算出 {len(weekly_data)} 周的数据")
        
        optimization_results = []
        cumulative_returns = []
        all_daily_returns = []  # 累积所有日收益率
        
        # 按周进行优化
        for week_idx, (week_end_date, weekly_return, daily_returns) in enumerate(weekly_data[:max_weeks]):
            if self.verbose:
                print(f"\n=== 第 {week_idx + 1} 周优化 (结束日期: {week_end_date}) ===")
            
            # 模拟该周的交易决策
            decision_result = self.make_trading_decision(week_end_date)
            
            # 计算累积收益率
            cumulative_returns.append(weekly_return)
            all_daily_returns.extend(daily_returns)  # 添加这一周的每日收益率
            
            # 基于累积的每日收益率计算Sharpe ratio
            current_sharpe = self.calculate_sharpe_ratio_from_daily_returns(all_daily_returns)
            
            if self.verbose:
                print(f"周收益率: {weekly_return:.4f} ({weekly_return*100:.2f}%)")
                print(f"本周交易天数: {len(daily_returns)}")
                print(f"累积交易天数: {len(all_daily_returns)}")
                print(f"当前Sharpe ratio (年化): {current_sharpe:.4f}")
                print(f"交易决策: {decision_result['decision']}")
                try:
                    confidence = float(decision_result['confidence'])
                    print(f"决策信心: {confidence:.2f}")
                except (ValueError, TypeError):
                    print(f"决策信心: {decision_result['confidence']}")
            
            # 使用Sharpe ratio作为奖励信号进行优化
            try:
                # 更新优化器
                self.optimizer.update(
                    prompt=self.trader_prompt,
                    traditional_score=current_sharpe * 100,  # 规范化分数
                    myerson_value=current_sharpe,
                    task_examples=[]
                )
                
                # 获取优化后的提示词
                optimization_result = self.optimizer.get_optimized_prompt(
                    current_prompt=self.trader_prompt,
                    current_traditional_score=current_sharpe * 100,
                    current_myerson_value=current_sharpe,
                    task_examples=[],
                    num_candidates=1
                )
                
                if optimization_result and optimization_result.get("optimized_prompt"):
                    old_prompt = self.trader_prompt
                    new_prompt = optimization_result["optimized_prompt"]
                    
                    if self.verbose:
                        print(f"\n🔄 提示词优化详情:")
                        print(f"   旧提示词长度: {len(old_prompt)}")
                        print(f"   新提示词长度: {len(new_prompt)}")
                        print(f"   优化状态: {optimization_result.get('status', 'unknown')}")
                        
                        # 显示提示词变化的关键部分
                        if old_prompt != new_prompt:
                            print(f"\n📝 提示词变化对比:")
                            print(f"   旧提示词开头: {old_prompt[:200]}...")
                            print(f"   新提示词开头: {new_prompt[:200]}...")
                            
                            # 找出主要差异
                            if len(new_prompt) > len(old_prompt):
                                print(f"   ✅ 提示词扩展了 {len(new_prompt) - len(old_prompt)} 个字符")
                            elif len(new_prompt) < len(old_prompt):
                                print(f"   ✂️ 提示词缩减了 {len(old_prompt) - len(new_prompt)} 个字符")
                            else:
                                print(f"   🔄 提示词长度不变，但内容有调整")
                        else:
                            print(f"   ℹ️ 提示词内容未发生变化")
                    
                    self.trader_prompt = new_prompt
                else:
                    if self.verbose:
                        print(f"   ⚠️ 优化失败或未产生新提示词")
                
            except Exception as e:
                print(f"优化失败: {e}")
            
            # 记录结果
            week_result = {
                "week": week_idx + 1,
                "end_date": week_end_date,
                "weekly_return": weekly_return,
                "cumulative_sharpe": current_sharpe,
                "week_trading_days": len(daily_returns),
                "cumulative_trading_days": len(all_daily_returns),
                "daily_returns": daily_returns,  # 记录这一周的每日收益率
                "decision": decision_result,
                "prompt_length": len(self.trader_prompt),
                "current_prompt": self.trader_prompt  # 保存当前完整提示词
            }
            optimization_results.append(week_result)
        
        # 计算最终统计
        final_sharpe = self.calculate_sharpe_ratio_from_daily_returns(all_daily_returns)
        total_return = sum(cumulative_returns)
        
        summary = {
            "total_weeks": len(optimization_results),
            "total_return": total_return,
            "final_sharpe_ratio": final_sharpe,
            "average_weekly_return": np.mean(cumulative_returns) if cumulative_returns else 0,
            "volatility": np.std(cumulative_returns) if len(cumulative_returns) > 1 else 0,
            "optimization_results": optimization_results
        }
        
        if self.verbose:
            print(f"\n=== 优化完成 ===")
            print(f"总周数: {summary['total_weeks']}")
            print(f"总收益率: {summary['total_return']:.4f} ({summary['total_return']*100:.2f}%)")
            print(f"最终Sharpe ratio: {summary['final_sharpe_ratio']:.4f}")
            print(f"平均周收益率: {summary['average_weekly_return']:.4f}")
            print(f"收益率波动率: {summary['volatility']:.4f}")
            
            # 显示提示词演进摘要
            self._print_prompt_evolution_summary(optimization_results)
        
        return summary
    
    def _print_prompt_evolution_summary(self, results: List[Dict]) -> None:
        """打印提示词演进摘要"""
        print(f"\n🧠 提示词演进摘要:")
        print(f"{'周数':<4} {'长度':<8} {'Sharpe':<10} {'提示词预览':<50}")
        print("="*80)
        
        for result in results:
            week = result['week']
            length = result['prompt_length']
            sharpe = result['cumulative_sharpe']
            prompt_preview = result['current_prompt'][:47] + "..." if len(result['current_prompt']) > 50 else result['current_prompt']
            
            print(f"{week:<4} {length:<8} {sharpe:<10.4f} {prompt_preview:<50}")
        
        # 计算提示词变化统计
        lengths = [r['prompt_length'] for r in results]
        initial_length = lengths[0]
        final_length = lengths[-1]
        max_length = max(lengths)
        min_length = min(lengths)
        
        print(f"\n📊 提示词变化统计:")
        print(f"   初始长度: {initial_length}")
        print(f"   最终长度: {final_length}")
        print(f"   最大长度: {max_length}")
        print(f"   最小长度: {min_length}")
        print(f"   总变化量: {final_length - initial_length:+d}")

def main():
    parser = argparse.ArgumentParser(description="周度交易员Sharpe ratio优化测试")
    parser.add_argument("--ticker", default="AAPL", help="股票代码 (默认: AAPL)")
    parser.add_argument("--weeks", type=int, default=12, help="最大测试周数 (默认: 12)")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 创建优化器
    optimizer = WeeklyTraderOptimizer(
        ticker=args.ticker,
        verbose=args.verbose
    )
    
    # 运行优化
    results = optimizer.run_optimization(max_weeks=args.weeks)
    
    # 保存结果
    output_file = f"weekly_trader_optimization_{args.ticker}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n结果已保存到: {output_file}")

if __name__ == "__main__":
    main() 
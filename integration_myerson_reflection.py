"""
Myerson值和POMDP集成

将基于Myerson值的贡献计算与POMDP提示词优化框架集成，
实现多智能体协作系统中的智能体提示优化。
"""
import os
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Set, Tuple
import numpy as np

# 导入自定义模块
from utils.myerson_calc import MyersonCalculator
from utils.reflection_module import ReflectionModule
from utils.pomdp_optimizer import POMDPOptimizer
from utils.llm_characteristic_function import LLMCharacteristicFunction
from utils.corrected_characteristic_function import CorrectedCharacteristicFunction
from utils.model_interface import ModelInterface

# 智能体描述常量
AGENT_DESCRIPTIONS = {
    "NAA": "新闻分析智能体，负责分析财经新闻并提取与股票相关的市场情绪",
    "TAA": "技术分析智能体，负责分析价格图表和技术指标，识别技术形态",
    "FAA": "基本面分析智能体，负责分析公司财务状况和基本面指标",
    "STCA": "短期综合分析智能体，整合新闻、技术和基本面分析，提供短期展望",
    "LTCA": "长期综合分析智能体，整合新闻、技术和基本面分析，提供长期展望",
    "BOA": "看多展望智能体，基于综合分析评估看多观点的可能性",
    "BeOA": "看空展望智能体，基于综合分析评估看空观点的可能性",
    "NOA": "中性展望智能体，基于综合分析评估中性观点的可能性",
    "TRA": "交易智能体，综合所有展望，制定最终交易决策"
}

# 智能体依赖关系图
AGENT_GRAPH = {
    "NAA": ["STCA", "LTCA"],
    "TAA": ["STCA", "LTCA"],
    "FAA": ["STCA", "LTCA"],
    "STCA": ["BOA", "BeOA", "NOA"],
    "LTCA": ["BOA", "BeOA", "NOA"],
    "BOA": ["TRA"],
    "BeOA": ["TRA"],
    "NOA": ["TRA"],
    "TRA": []
}

class MyersonPOMDPIntegration:
    """
    Myerson值和POMDP集成类
    
    结合Myerson值计算和POMDP优化框架，优化多智能体系统中的智能体提示词
    """
    
    def __init__(self, agents: Dict[str, Any], agent_graph: Dict[str, List[str]], 
                 model_interface=None, use_llm_characteristic: bool = True,
                 optimizer_type: str = "pomdp", verbose: bool = False,
                 ticker: str = "UNKNOWN"):
        """
        初始化 Myerson-POMDP 集成模块
        
        参数:
            agents: 智能体字典
            agent_graph: 智能体依赖关系图
            model_interface: 模型接口，用于LLM特征函数和反思
            use_llm_characteristic: 是否使用LLM特征函数
            optimizer_type: 优化器类型，可选："pomdp"、"gpo"、"opro"
            verbose: 是否详细输出
            ticker: 股票代码
        """
        self.agents = agents
        self.agent_graph = agent_graph
        self.model_interface = model_interface
        self.use_llm_characteristic = use_llm_characteristic
        self.optimizer_type = optimizer_type  # 新增优化器类型
        self.verbose = verbose
        self.ticker = ticker  # 添加ticker属性
        
        # 初始化 Myerson 值计算器
        self.myerson_calculator = MyersonCalculator(agent_graph)
        
        # 为每个智能体初始化反思模块
        self.reflection_modules = {}
        for agent_id, agent in agents.items():
            self.reflection_modules[agent_id] = ReflectionModule(
                agent_id=agent_id,
                agent_type=agent.agent_type,
                model_interface=model_interface
            )
        
        # 存储智能体的当前提示
        self.agent_prompts = {}
        for agent_id in agents.keys():
            if hasattr(agents[agent_id], 'current_prompt_template'):
                self.agent_prompts[agent_id] = agents[agent_id].current_prompt_template
            else:
                # 如果没有当前提示模板，使用默认提示
                default_prompt = agents[agent_id].get_default_prompt_template() if hasattr(agents[agent_id], 'get_default_prompt_template') else ""
                self.agent_prompts[agent_id] = default_prompt
        
        # 历史 Myerson 值
        self.previous_myerson_values = {agent_id: 0.0 for agent_id in agents.keys()}
        
        # 期间历史
        self.period_history = []
        self.current_period_id = None
        self.previous_period_id = None
        
        # 当前状态和数据
        self.current_state = None
        self.current_day_data = None
        
        # 修正的特征函数（基于实际智能体运行）
        self.corrected_characteristic_function = None
        if self.agents:
            self.corrected_characteristic_function = CorrectedCharacteristicFunction(
                agents=self.agents,
                agent_graph=AGENT_GRAPH,
                verbose=self.verbose
            )
    
    def _load_initial_prompts(self) -> Dict[str, str]:
        """加载初始提示词"""
        prompts = {}
        for agent_id in AGENT_GRAPH.keys():
            # 尝试加载保存的提示词，如不存在则使用默认提示词
            prompt_path = os.path.join("logs", "optimizer", agent_id, f"{agent_id}_best_prompt.txt")
            if os.path.exists(prompt_path):
                try:
                    with open(prompt_path, 'r') as f:
                        prompts[agent_id] = f.read()
                    if self.verbose:
                        print(f"已加载 {agent_id} 智能体的保存提示词")
                except:
                    prompts[agent_id] = self._get_default_prompt(agent_id)
            else:
                prompts[agent_id] = self._get_default_prompt(agent_id)
        
        return prompts
    
    def _get_default_prompt(self, agent_id: str) -> str:
        """获取默认提示词"""
        # 实际应用中应根据智能体类型返回合适的默认提示词
        if agent_id == "NAA":
            return """
作为新闻分析智能体，请分析以下交易日的新闻，并提供关于{ticker}的市场情绪和可能的投资影响。

分析要求：
1. 识别关键新闻事件并按重要性排序
2. 评估每条新闻的情绪倾向（积极、中性或消极）
3. 特别关注与{ticker}直接相关的新闻
4. 分析新闻与之前交易日的连续性
5. 考虑新闻的可信度和市场影响力

新闻数据：
{news_data}

请提供结构化分析：
- 关键事件摘要
- 整体市场情绪评估（1-10分，1为极度负面，10为极度正面）
- 针对{ticker}的特定影响分析
- 可能影响投资决策的最重要因素
- 信心评级（1-5星，5星表示高度确信）
"""
        elif agent_id == "TAA":
            return """
作为技术分析智能体，请分析以下{ticker}的价格与交易数据，并提供技术面评估和短期预测。

分析要求：
1. 识别关键的价格支撑位与阻力位
2. 评估主要技术指标（MACD, RSI, 移动平均线等）的信号
3. 识别图表形态和潜在的突破点
4. 分析交易量与价格关系
5. 考虑市场周期和季节性因素

价格数据：
{ohlcv_data}

请提供结构化分析：
- 趋势分析（短期、中期和长期趋势状态）
- 关键技术指标解读
- 支撑位和阻力位水平
- 技术形态识别
- 交易量分析
- 未来5天的价格走势预测
- 技术分析置信度评级（1-5星）
"""
        # 可以继续添加其他智能体的默认提示词
        else:
            return f"作为{AGENT_DESCRIPTIONS.get(agent_id, agent_id)}，请分析相关数据并提供专业见解。"
    
    def initialize_optimizers(self) -> None:
        """初始化优化器（根据optimizer_type选择相应的优化器类型）"""
        if self.optimizer_type == "pomdp":
            self.pomdp_optimizers = self._initialize_pomdp_optimizers()
        elif self.optimizer_type == "gpo":
            self.gpo_optimizers = self._initialize_gpo_optimizers()
        elif self.optimizer_type == "opro":
            self.opro_optimizers = self._initialize_opro_optimizers()
        else:
            raise ValueError(f"不支持的优化器类型: {self.optimizer_type}")
            
        if self.verbose:
            print(f"已初始化 {self.optimizer_type.upper()} 优化器，共 {len(self.agents)} 个智能体")

    def _initialize_pomdp_optimizers(self) -> Dict[str, POMDPOptimizer]:
        """
        初始化POMDP优化器
        
        返回:
            POMDP优化器字典
        """
        optimizers = {}
        for agent_id in self.agents.keys():
            optimizers[agent_id] = POMDPOptimizer(
                agent_id=agent_id,
                base_prompt=self.agent_prompts[agent_id],
                reflection_module=self.reflection_modules[agent_id],
                exploration_rate=0.2,
                history_window=3,
                plateau_patience=3,
                load_best_prompt=True,
                log_dir=os.path.join("logs", "optimizer", agent_id)
            )
        return optimizers

    def _initialize_gpo_optimizers(self) -> Dict[str, 'GPOOptimizer']:
        """
        初始化GPO优化器
        
        返回:
            GPO优化器字典
        """
        from utils.gpo_optimizer import GPOOptimizer
        
        optimizers = {}
        for agent_id, agent in self.agents.items():
            optimizer = GPOOptimizer(
                agent_id, 
                agent.agent_type,
                model_interface=self.model_interface,
                max_optimization_steps=20,
                history_prompts_k=3,
                initial_max_edit_words=50,
                plateau_patience=3
            )
            optimizers[agent_id] = optimizer
        return optimizers

    def _initialize_opro_optimizers(self) -> Dict[str, 'OPROOptimizer']:
        """
        初始化OPRO优化器
        
        返回:
            OPRO优化器字典
        """
        from utils.opro_optimizer import OPROOptimizer
        
        optimizers = {}
        for agent_id, agent in self.agents.items():
            optimizer = OPROOptimizer(
                agent_id, 
                agent.agent_type,
                model_interface=self.model_interface,
                max_optimization_steps=20,
                history_prompts_k=8,  # OPRO通常使用更多历史
                max_num_generated_instructions=4,
                plateau_patience=3
            )
            optimizers[agent_id] = optimizer
        return optimizers
    
    def start_period(self, period_id: str, market_context: str, actual_return: float, state: Dict[str, Any] = None) -> None:
        """
        开始新的评估期间
        
        参数:
            period_id: 期间ID
            market_context: 市场上下文描述
            actual_return: 实际收益率
            state: 当前状态（包含市场数据等）
        """
        self.previous_period_id = self.current_period_id
        self.current_period_id = period_id
        
        # 存储当前状态
        self.current_state = state or {
            "ticker": self.ticker,
            "market_context": market_context,
            "period_id": period_id
        }
        
        # 记录期间信息
        self.period_history.append({
            "period_id": period_id,
            "timestamp": datetime.now().isoformat(),
            "ticker": self.ticker,
            "actual_return": actual_return,
            "market_context": market_context
        })
        
        if self.verbose:
            print(f"\n开始期间 {period_id}, 股票: {self.ticker}, 实际收益率: {actual_return:.2%}")
    
    def _run_agent_subset(self, agent_subset: Set[str], state: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行智能体子集，正确处理被排除的智能体
        
        参数:
            agent_subset: 要运行的智能体子集
            state: 输入状态
            
        返回:
            包含智能体输出和最终交易决策的结果
        """
        results = {}
        
        # 按照依赖关系顺序运行智能体
        execution_order = self._get_execution_order()
        
        for agent_id in execution_order:
            if agent_id in agent_subset and agent_id in self.agents:
                # 智能体在子集中，正常运行
                try:
                    agent_result = self.agents[agent_id].process(state)
                    results[agent_id] = agent_result
                    
                    # 更新状态以传递给下游智能体
                    state[f"{agent_id}_output"] = agent_result
                    
                except Exception as e:
                    print(f"运行智能体 {agent_id} 时出错: {e}")
                    results[agent_id] = None
                    state[f"{agent_id}_output"] = None
            else:
                # 智能体被排除，设置为None或默认行为
                if agent_id == "TRA":
                    # 交易智能体被排除时，默认行为是"保持不变"
                    results[agent_id] = {
                        "action": "hold",
                        "confidence": 0.0,
                        "reasoning": "智能体被排除，默认保持不变"
                    }
                    state[f"{agent_id}_output"] = results[agent_id]
                else:
                    # 其他智能体被排除时，输出为None
                    results[agent_id] = None
                    state[f"{agent_id}_output"] = None
        
        return results
    
    def _get_execution_order(self) -> List[str]:
        """
        根据智能体依赖关系图确定执行顺序
        
        返回:
            按拓扑顺序排列的智能体ID列表
        """
        import networkx as nx
        
        # 创建有向图
        G = nx.DiGraph()
        for agent_id, dependencies in AGENT_GRAPH.items():
            G.add_node(agent_id)
            for dep in dependencies:
                G.add_edge(agent_id, dep)
        
        # 使用拓扑排序确定执行顺序
        try:
            execution_order = list(nx.topological_sort(G))
            return execution_order
        except nx.NetworkXError:
            # 如果存在环，使用默认顺序
            return ["NAA", "TAA", "FAA", "STCA", "LTCA", "BOA", "BeOA", "NOA", "TRA"]
    
    def _calculate_subset_return(self, agent_subset: Set[str], state: Dict[str, Any]) -> float:
        """
        计算智能体子集的收益率
        
        参数:
            agent_subset: 智能体子集
            state: 输入状态
            
        返回:
            预估收益率
        """
        # 如果有修正的特征函数，使用它
        if self.corrected_characteristic_function:
            return self.corrected_characteristic_function.evaluate_subset(agent_subset, state)
        
        # 否则使用简化的计算方法
        if not agent_subset:
            return 0.0
        
        # 运行智能体子集
        results = self._run_agent_subset(agent_subset, state.copy())
        
        # 获取交易决策
        trader_result = results.get("TRA")
        if not trader_result:
            return 0.0
        
        # 根据交易决策计算预估收益
        action = trader_result.get("action", "hold")
        confidence = trader_result.get("confidence", 0.0)
        
        # 简化的收益计算：基于行动和信心度
        if action == "buy":
            return confidence * 0.02  # 最多2%的正收益
        elif action == "sell":
            return -confidence * 0.02  # 最多2%的负收益
        else:  # hold
            return 0.0
    
    def compute_myerson_values_correct(self, state: Dict[str, Any], actual_return: float) -> Dict[str, float]:
        """
        正确计算Myerson值（Shapley值）
        
        参数:
            state: 当前状态
            actual_return: 实际收益率
            
        返回:
            各智能体的Myerson值字典
        """
        if self.verbose:
            print("开始正确的Myerson值计算（基于实际智能体运行）...")
        
        agents = list(AGENT_GRAPH.keys())
        n = len(agents)
        myerson_values = {agent_id: 0.0 for agent_id in agents}
        
        # 预计算权重
        weights = self._calculate_coalition_weights(n)
        
        # 为每个智能体计算其边际贡献
        for i, agent in enumerate(agents):
            if self.verbose:
                print(f"计算智能体 {agent} 的Myerson值...")
            
            # 遍历不包含当前智能体的所有子集
            other_agents = [a for a in agents if a != agent]
            subsets = self._powerset(other_agents)
            
            for subset_tuple in subsets:
                S = set(subset_tuple)
                S_with_agent = S.union({agent})
                
                # 计算特征函数值
                v_S = self._calculate_subset_return(S, state)
                v_S_with_agent = self._calculate_subset_return(S_with_agent, state)
                
                # 计算边际贡献
                marginal_contribution = v_S_with_agent - v_S
                
                # 计算权重
                weight = weights[len(S)]
                
                # 累加到Myerson值
                myerson_values[agent] += weight * marginal_contribution
                
                if self.verbose and len(S) <= 2:  # 只打印小子集的详细信息
                    print(f"  子集 S={S}, S∪{{{agent}}}={S_with_agent}")
                    print(f"    v(S)={v_S:.4f}, v(S∪{{{agent}}})={v_S_with_agent:.4f}")
                    print(f"    边际贡献={marginal_contribution:.4f}, 权重={weight:.4f}")
        
        # 标准化Myerson值为收益贡献
        total_myerson = sum(abs(v) for v in myerson_values.values())
        if total_myerson > 0:
            # 按比例分配实际收益
            contribution_ratios = {k: v / total_myerson for k, v in myerson_values.items()}
            contributions = {k: ratio * actual_return for k, ratio in contribution_ratios.items()}
        else:
            # 如果总Myerson值为0，平均分配
            contributions = {k: actual_return / len(agents) for k in agents}
        
        if self.verbose:
            print("Myerson值计算完成:")
            for agent_id in sorted(myerson_values.keys()):
                print(f"  {agent_id}: Myerson值={myerson_values[agent_id]:.4f}, "
                     f"收益贡献={contributions[agent_id]:.4f} ({contributions[agent_id]/actual_return*100:.1f}%)")
        
        return contributions
    
    def _powerset(self, iterable: List[str]) -> List[Tuple[str, ...]]:
        """
        生成集合的所有子集
        
        参数:
            iterable: 输入集合
            
        返回:
            所有可能的子集列表
        """
        from itertools import combinations, chain
        s = list(iterable)
        return list(chain.from_iterable(combinations(s, r) for r in range(len(s) + 1)))
    
    def _calculate_coalition_weights(self, n: int) -> List[float]:
        """
        预计算联盟权重
        权重公式为: |S|! * (N-|S|-1)! / N!
        """
        import math
        
        if n == 0:
            return []
        
        weights = [0.0] * n
        n_factorial = math.factorial(n)
        
        for s_size in range(n):
            try:
                s_factorial = math.factorial(s_size)
                n_minus_s_minus_1_factorial = math.factorial(n - s_size - 1)
                weights[s_size] = (s_factorial * n_minus_s_minus_1_factorial) / n_factorial
            except ValueError:
                weights[s_size] = 0.0
                
        return weights
    
    def compute_myerson_values(self) -> Dict[str, float]:
        """
        计算当前期间的Myerson值（使用正确的方法）
        
        返回:
            各智能体的贡献字典
        """
        if not self.current_period_id or not self.current_state:
            raise ValueError("必须先调用start_period初始化期间")
        
        period_info = next((p for p in self.period_history if p["period_id"] == self.current_period_id), None)
        if not period_info:
            raise ValueError(f"找不到期间 {self.current_period_id} 的信息")
        
        # 使用正确的Myerson值计算
        start_time = time.time()
        contributions = self.compute_myerson_values_correct(
            self.current_state, 
            period_info["actual_return"]
        )
        end_time = time.time()
        
        if self.verbose:
            print(f"正确的Myerson值计算完成, 耗时: {end_time - start_time:.2f}秒")
            print("各智能体的收益贡献:")
            for agent_id, contribution in sorted(contributions.items()):
                percentage = contribution / period_info["actual_return"] * 100 if period_info["actual_return"] != 0 else 0
                print(f"  {agent_id}: {contribution:.4f} ({percentage:.1f}%)")
        
        return contributions
    
    def compute_contribution_gradients(self) -> Dict[str, float]:
        """
        计算贡献梯度（当期与上期贡献的差值）
        
        返回:
            各智能体的贡献梯度字典
        """
        if not self.current_period_id or not self.previous_period_id:
            # 如果是第一个期间，返回零梯度
            return {agent_id: 0.0 for agent_id in AGENT_GRAPH.keys()}
        
        gradients = self.myerson_calculator.calculate_contribution_gradients(
            current_period_id=self.current_period_id,
            previous_period_id=self.previous_period_id
        )
        
        if self.verbose:
            print("\n各智能体的贡献梯度:")
            for agent_id, gradient in sorted(gradients.items()):
                print(f"  {agent_id}: {gradient:.4f} ({'+' if gradient >= 0 else ''}{gradient:.2%})")
        
        return gradients
    
    def update_reflection_histories(self, agent_outputs: Dict[str, str], contributions: Dict[str, float], 
                                   gradients: Dict[str, float]) -> None:
        """
        更新智能体反思历史（修改奖励计算）
        
        参数:
            agent_outputs: 智能体输出字典
            contributions: 贡献字典
            gradients: 贡献梯度字典
        """
        period_info = next((p for p in self.period_history if p["period_id"] == self.current_period_id), None)
        if not period_info:
            return
        
        for agent_id, reflection_module in self.reflection_modules.items():
            if agent_id in contributions:
                # 计算奖励：今天的收益率 × Myerson计算的贡献百分比
                contribution_percentage = contributions[agent_id] / period_info["actual_return"] if period_info["actual_return"] != 0 else 0
                reward = period_info["actual_return"] * contribution_percentage
                
                # 创建POMDP状态表示
                pomdp_state = {
                    "period_id": self.current_period_id,
                    "ticker": self.ticker,
                    "actual_return": period_info["actual_return"],
                    "contribution": contributions[agent_id],
                    "contribution_percentage": contribution_percentage,
                    "gradient": gradients.get(agent_id, 0.0)
                }
                
                # 向反思模块添加历史数据
                reflection_module.add_history_data(
                    prompt=self.agent_prompts[agent_id],
                    context={"ticker": self.ticker, "market_context": period_info["market_context"]},
                    output=agent_outputs.get(agent_id, ""),
                    shapley_value=contributions[agent_id],
                    reward=reward,  # 使用修正的奖励计算
                    contribution_percentage=contribution_percentage,
                    actual_return=period_info["actual_return"],
                    contribution_gradient=gradients.get(agent_id, 0.0),
                    pomdp_state=pomdp_state
                )
    
    def optimize_prompts(self, contributions: Dict[str, float], gradients: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """
        优化智能体提示词
        
        参数:
            contributions: 贡献字典
            gradients: 贡献梯度字典
            
        返回:
            优化结果字典
        """
        optimization_results = {}
        
        if self.optimizer_type == "pomdp":
            optimizers = getattr(self, 'pomdp_optimizers', {})
        elif self.optimizer_type == "gpo":
            optimizers = getattr(self, 'gpo_optimizers', {})
        elif self.optimizer_type == "opro":
            optimizers = getattr(self, 'opro_optimizers', {})
        else:
            raise ValueError(f"不支持的优化器类型: {self.optimizer_type}")
        
        for agent_id, optimizer in optimizers.items():
            # 计算当前分数（可以是贡献值或其他指标）
            current_score = contributions.get(agent_id, 0.0)
            gradient = gradients.get(agent_id, 0.0)
            
            if self.optimizer_type == "pomdp":
                # POMDP优化器
                update_result = optimizer.update(current_score, gradient)
                
                # 获取优化后的提示词
                if update_result["status"] != "plateau":
                    optimized_prompt = optimizer.get_next_prompt()
                    self.agent_prompts[agent_id] = optimized_prompt
                
                # 记录优化结果
                optimization_results[agent_id] = {
                    "status": update_result["status"],
                    "score": current_score,
                    "gradient": gradient,
                    "best_score": update_result["best_score"]
                }
                
            elif self.optimizer_type in ["gpo", "opro"]:
                # GPO和OPRO优化器
                current_prompt = self.agent_prompts[agent_id]
                score = min(max(current_score * 100, 0), 100)  # 规范化到0-100范围
                
                try:
                    # 更新优化轨迹
                    optimizer.update(current_prompt, score)
                    
                    # 生成优化的提示
                    optimization_result = optimizer.get_optimized_prompt(
                        current_prompt, 
                        score, 
                        task_examples=[],  # 这里可以传入任务示例
                        num_candidates=1
                    )
                    
                    if "optimized_prompt" in optimization_result and optimization_result["status"] == "success":
                        # 更新智能体的提示模板
                        self.agent_prompts[agent_id] = optimization_result["optimized_prompt"]
                    elif optimization_result.get('status') == "plateau" and "best_prompt" in optimization_result:
                        # 确保使用最佳提示
                        self.agent_prompts[agent_id] = optimization_result["best_prompt"]
                    
                    # 记录优化结果
                    optimization_results[agent_id] = {
                        "status": optimization_result.get("status", "unknown"),
                        "score": current_score,
                        "gradient": gradient,
                        "details": optimization_result.get("details", "")
                    }
                    
                except Exception as e:
                    print(f"智能体 {agent_id} 的{self.optimizer_type.upper()}优化过程中出错: {e}")
                    optimization_results[agent_id] = {
                        "status": "error",
                        "score": current_score,
                        "gradient": gradient,
                        "error": str(e)
                    }
                    
            if self.verbose:
                status = optimization_results[agent_id]["status"]
                status_msg = {
                    "plateau": "停滞",
                    "exploring": "探索中",
                    "exploiting": "利用中",
                    "success": "成功优化",
                    "error": "优化错误"
                }.get(status, status)
                print(f"智能体 {agent_id} 的{self.optimizer_type.upper()}优化状态: {status}, {status_msg}")
        
        return optimization_results
    
    def run_demo(self, num_periods: int = 10) -> None:
        """
        运行演示，模拟多个期间的优化过程
        
        参数:
            num_periods: 期间数量
        """
        from random import uniform
        
        # 检查是否有智能体
        if not self.agents:
            print("警告：没有提供智能体，演示将使用模拟数据")
            return
        
        # 初始化优化器
        self.initialize_optimizers()
        
        for i in range(num_periods):
            period_id = f"period_{i+1}"
            
            # 模拟实际收益率（实际应用中应从实际数据获取）
            actual_return = uniform(-0.03, 0.03)  # 随机生成-3%到3%之间的收益率
            
            # 模拟市场上下文
            market_context = f"这是第{i+1}个交易日的市场上下文，市场整体{('下跌' if actual_return < 0 else '上涨')}。"
            
            # 模拟状态数据
            state = {
                "ticker": self.ticker,
                "market_context": market_context,
                "period_id": period_id,
                "day": i + 1,
                "price_data": {"open": 100 + i, "close": 100 + i + actual_return * 100},
                "news_data": [f"模拟新闻 {i+1}"],
                "technical_data": {"rsi": 50 + i % 20, "macd": 0.1 * actual_return}
            }
            
            # 开始新期间
            self.start_period(period_id, market_context, actual_return, state)
            
            # 如果有智能体，运行完整的智能体管道获取真实输出
            if self.agents:
                full_results = self._run_agent_subset(set(AGENT_GRAPH.keys()), state.copy())
                agent_outputs = {aid: str(result) for aid, result in full_results.items() if result}
            else:
                # 模拟智能体输出
                agent_outputs = {agent_id: f"智能体 {agent_id} 在期间 {period_id} 的分析输出" 
                               for agent_id in AGENT_GRAPH.keys()}
            
            # 计算Myerson值（使用正确的方法）
            contributions = self.compute_myerson_values()
            
            # 计算贡献梯度
            gradients = self.compute_contribution_gradients()
            
            # 更新反思历史
            self.update_reflection_histories(agent_outputs, contributions, gradients)
            
            # 优化提示词
            optimization_results = self.optimize_prompts(contributions, gradients)
            
            print(f"\n完成期间 {period_id} 的正确Myerson-POMDP优化循环")
            print(f"期间收益率: {actual_return:.2%}")
            
            # 模拟间隔
            time.sleep(1)

# 如果作为主程序运行，执行示例
if __name__ == "__main__":
    print("=== 正确的Myerson值计算演示 ===")
    print("这个演示展示如何正确计算Shapley值（Myerson值）:")
    print("1. 遍历所有智能体子集")
    print("2. 正确处理被排除的智能体（输出None或默认行为）")
    print("3. 基于实际智能体运行结果计算特征函数")
    print("4. 使用Myerson值百分比乘以当天收益率作为奖励")
    print("5. 使用GPO方法优化提示词\n")
    
    # 创建集成实例（没有真实智能体的简化演示）
    integration = MyersonPOMDPIntegration(
        run_id=f"correct_myerson_demo_{int(time.time())}",
        ticker="AAPL",
        verbose=True
    )
    
    print("注意：这是一个简化演示，展示正确的计算逻辑。")
    print("在实际应用中，需要传入真实的智能体和环境实例。\n")
    
    # 运行简化演示
    try:
        integration.run_demo(num_periods=2)
    except Exception as e:
        print(f"演示过程中遇到预期的错误（因为没有真实智能体）: {e}")
        print("\n要使用完整功能，请参考以下示例代码:")
        print("""
# 完整使用示例：
from multi_agent_coordinator import MultiAgentCoordinator

# 创建协调器并获取智能体
config = {"env_config": {"stocks": ["AAPL"]}, "verbose": True}
coordinator = MultiAgentCoordinator(config)

# 创建正确的Myerson-POMDP集成
integration = MyersonPOMDPIntegration(
    run_id="real_run_example",
    ticker="AAPL",
    agents=coordinator.agents,  # 传入真实智能体
    env=coordinator.env,        # 传入交易环境
    verbose=True
)

# 运行真实的优化过程
integration.run_demo(num_periods=10)
        """)

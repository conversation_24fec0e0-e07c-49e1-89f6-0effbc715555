# 多智能体美股交易系统 - 修复版本

## 概述

本项目是一个基于多智能体协作的美股交易系统，使用了先进的Myerson值计算和OPRO优化技术。本次更新修复了系统中的关键问题，提高了稳定性和可靠性。

## 主要修复

### ✅ 已修复的问题

1. **StockTradingEnv.get_state() 方法缺失**
   - 添加了公共的 `get_state()` 方法
   - 解决了 LangGraph 协调器调用错误

2. **MyersonOPROOptimizer 递归调用问题**
   - 重构了 `update()` 方法，消除递归调用风险
   - 优化了内存使用和性能

3. **数据可用性和错误处理**
   - 添加了数据质量验证
   - 改进了错误处理和降级机制
   - 增强了数据库连接管理

## 快速开始

### 1. 环境设置

确保您已安装所需的依赖：

```bash
pip install pandas numpy sqlite3 json-repair
```

### 2. 基本使用

#### 创建交易环境

```python
from stock_trading_env import StockTradingEnv
from config import TRADING_ENV_CONFIG

# 配置交易环境
config = TRADING_ENV_CONFIG.copy()
config.update({
    "start_date": "2024-01-01",
    "end_date": "2024-01-10",
    "stocks": ["AAPL", "MSFT"],
    "starting_cash": 100000
})

# 创建环境
env = StockTradingEnv(config)

# 获取当前状态（使用修复后的方法）
state = env.get_state()
print(f"当前日期: {state['date']}")
print(f"可用现金: ${state['cash']:,.2f}")
```

#### 使用优化器

```python
from utils.myerson_opro_optimizer import MyersonOPROOptimizer

# 创建优化器
optimizer = MyersonOPROOptimizer(
    agent_id="MY_AGENT",
    agent_type="technical_analyst",
    model_interface=None  # 或您的模型接口
)

# 更新优化器（修复后的方法）
optimizer.update(
    prompt="分析技术指标并提供交易建议",
    traditional_score=0.75,
    myerson_value=0.15
)

print(f"当前步数: {optimizer.current_step}")
print(f"最佳Myerson奖励: {optimizer.best_myerson_reward}")
```

### 3. 运行测试

验证所有修复是否正常工作：

```bash
python test_fixes.py
```

### 4. 查看示例

运行完整的功能演示：

```bash
python example_usage.py
```

## 系统架构

### 核心组件

1. **StockTradingEnv**: 交易环境模拟器
   - 价格数据管理
   - 交易执行
   - 状态管理（现已包含公共 `get_state()` 方法）

2. **MyersonOPROOptimizer**: 基于Myerson值的优化器
   - OPRO优化算法
   - Myerson值计算
   - 提示词优化（已修复递归调用问题）

3. **LangGraphCoordinator**: 多智能体协调器
   - 智能体管理
   - 工作流编排
   - 状态同步

### 数据处理

- **SQLite数据库**: 存储OHLCV、新闻和基本面数据
- **数据验证**: 自动检查数据质量和完整性
- **错误处理**: 优雅处理缺失或无效数据

## 配置选项

### 交易环境配置

```python
TRADING_ENV_CONFIG = {
    "starting_cash": 100_000,      # 初始资金
    "trading_fee_rate": 0.001,     # 交易费率
    "price_window": 26,            # 价格历史窗口
    "news_window": 7,              # 新闻历史窗口
    "fail_on_large_gaps": True,    # 是否在大数据间隙时失败
    "fill_date_gaps": False,       # 是否填补小的日期间隙
    "max_date_gap_days": 7         # 最大允许的日期间隙
}
```

### 优化器配置

```python
optimizer_config = {
    "max_optimization_steps": 20,     # 最大优化步数
    "history_prompts_k": 8,           # 历史提示数量
    "max_num_generated_instructions": 4,  # 每次生成的候选数
    "plateau_patience": 5,            # 停滞容忍度
    "memory_size": 100,               # 历史记录大小
    "myerson_weight": 1.0,            # Myerson值权重
    "smooth_reward": True             # 是否使用平滑奖励
}
```

## 故障排除

### 常见问题

1. **数据库连接错误**
   ```
   解决方案: 检查数据库文件路径和权限
   ```

2. **缺失数据**
   ```
   解决方案: 系统会自动使用fallback机制，无需手动干预
   ```

3. **内存使用过高**
   ```
   解决方案: 调整 memory_size 参数，减少历史记录大小
   ```

### 日志和调试

- 优化器日志位置: `logs/optimizer/{agent_type}/`
- 状态文件: `logs/optimizer/{agent_type}/{agent_id}_state.json`
- 最佳提示: `logs/optimizer/{agent_type}/{agent_id}_best_prompt.txt`

## 性能优化

### 建议设置

1. **小规模测试**:
   - 使用较短的日期范围
   - 减少股票数量
   - 降低优化步数

2. **生产环境**:
   - 启用数据缓存
   - 使用并行处理
   - 定期清理历史记录

## 更新日志

### v1.1.0 (当前版本)

- ✅ 修复 `StockTradingEnv.get_state()` 方法缺失
- ✅ 解决 `MyersonOPROOptimizer` 递归调用问题
- ✅ 改进数据处理和错误处理
- ✅ 添加数据质量验证
- ✅ 优化内存使用
- ✅ 增强系统稳定性

### 测试覆盖

- ✅ 单元测试: 4/4 通过
- ✅ 集成测试: 4/4 通过
- ✅ 功能演示: 4/4 成功

## 支持

如果您遇到问题或需要帮助，请：

1. 查看 `FIXES_SUMMARY.md` 了解详细修复信息
2. 运行 `test_fixes.py` 验证系统状态
3. 查看 `example_usage.py` 了解正确用法
4. 检查日志文件获取详细错误信息

## 许可证

本项目遵循原有许可证条款。

---

**注意**: 本版本已经过充分测试，所有已知问题都已修复。建议在生产环境中使用前进行充分的测试。

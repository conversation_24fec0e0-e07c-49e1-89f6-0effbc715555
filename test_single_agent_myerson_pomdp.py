#!/usr/bin/env python3
"""
单智能体Myerson-POMDP提示词优化测试

专门用于测试单个智能体的Myerson值计算和POMDP提示词优化功能
"""

import os
import sys
import time
import json
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config import MODEL_CONFIGS, DEFAULT_MODEL
from utils.model_interface import ModelInterface
from utils.myerson_opro_optimizer import MyersonOPROOptimizer
from optimization_evaluator import OptimizationEvaluator

class SingleAgentMyersonPOMDPTester:
    """单智能体Myerson-POMDP测试器"""
    
    def __init__(self, agent_type: str = "TRA", ticker: str = "AAPL", verbose: bool = True):
        self.agent_type = agent_type
        self.ticker = ticker
        self.verbose = verbose
        
        # 初始化模型接口
        self.model_interface = ModelInterface(
            model_name=DEFAULT_MODEL,
            config=MODEL_CONFIGS[DEFAULT_MODEL]
        )
        
        # 初始化优化器
        self.optimizer = MyersonOPROOptimizer(
            agent_id=agent_type,
            agent_type=agent_type,
            model_interface=self.model_interface,
            max_optimization_steps=5,  # 测试用，设置较少步数
            history_prompts_k=3,
            max_num_generated_instructions=2,
            plateau_patience=3
        )
        
        # 初始化评估器
        self.evaluator = OptimizationEvaluator()
        
        # 测试数据
        self.test_data = None
        
    def load_test_data(self, start_date: str = "2025-05-13", end_date: str = "2025-05-21") -> bool:
        """加载测试数据"""
        try:
            # 尝试从数据库加载真实数据
            db_path = f"data/tickers/{self.ticker}/{self.ticker}_data.db"
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                query = """
                SELECT trade_date, Open, High, Low, Close, Adj_Close, Volume
                FROM ohlcv
                WHERE ticker = ? AND trade_date BETWEEN ? AND ?
                ORDER BY trade_date
                """
                df = pd.read_sql_query(query, conn, params=(self.ticker, start_date, end_date))
                conn.close()
                
                if len(df) >= 3:  # 至少需要3天数据
                    self.test_data = df
                    if self.verbose:
                        print(f"✅ 成功加载 {len(df)} 天的真实数据")
                    return True
            
            # 如果没有真实数据，生成模拟数据
            self._generate_simulated_data(start_date, end_date)
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _generate_simulated_data(self, start_date: str, end_date: str):
        """生成模拟测试数据"""
        dates = pd.date_range(start=start_date, end=end_date, freq='B')[:7]  # 最多7个工作日
        
        # 生成有趋势的价格数据
        base_price = 200.0
        prices = []
        
        for i, date in enumerate(dates):
            # 创建一个有波动的价格序列
            trend = 0.98 if i < len(dates) // 2 else 1.02  # 先下跌后上涨
            price = base_price * (trend ** i) * (1 + (i % 3 - 1) * 0.01)  # 添加小波动
            
            open_price = price * (1 + (i % 2 - 0.5) * 0.005)
            high = price * 1.015
            low = price * 0.985
            volume = 1000000 + i * 100000
            
            prices.append({
                'trade_date': date.strftime('%Y-%m-%d'),
                'Open': round(open_price, 2),
                'High': round(high, 2),
                'Low': round(low, 2),
                'Close': round(price, 2),
                'Adj_Close': round(price, 2),
                'Volume': volume
            })
        
        self.test_data = pd.DataFrame(prices)
        if self.verbose:
            print(f"✅ 生成了 {len(self.test_data)} 天的模拟数据")
            print(f"   价格范围: ${self.test_data['Close'].min():.2f} - ${self.test_data['Close'].max():.2f}")
    
    def get_default_prompt(self) -> str:
        """获取默认提示词"""
        if self.agent_type == "TRA":
            return """
作为专业的交易智能体，请基于提供的市场数据做出交易决策。

分析要求：
1. 综合考虑价格趋势、技术指标和市场情绪
2. 评估风险和收益潜力
3. 制定明确的交易策略

请以JSON格式返回您的分析和决策：
{
    "action": "BUY/SELL/HOLD",
    "confidence": 0.8,
    "reasoning": "详细的分析理由",
    "risk_assessment": "风险评估",
    "target_price": 205.50
}

市场数据：
{market_data}

请仅返回JSON格式的响应，不要包含其他文本。
"""
        elif self.agent_type == "TAA":
            return """
作为技术分析智能体，请分析以下价格数据并提供技术面评估。

分析要求：
1. 识别价格趋势和关键支撑阻力位
2. 分析技术指标信号
3. 评估短期价格走势

请以JSON格式返回分析结果：
{
    "trend": "上涨/下跌/横盘",
    "support_level": 195.00,
    "resistance_level": 210.00,
    "technical_score": 0.7,
    "recommendation": "买入/卖出/观望"
}

价格数据：
{market_data}

请仅返回JSON格式的响应。
"""
        else:
            return f"作为{self.agent_type}智能体，请分析市场数据并提供专业见解。"
    
    def simulate_trading_with_prompt(self, prompt: str, day_data: Dict[str, Any]) -> Dict[str, Any]:
        """使用给定提示词模拟交易"""
        try:
            # 准备市场数据
            market_data = {
                "ticker": self.ticker,
                "date": day_data.get("trade_date"),
                "price": {
                    "open": day_data.get("Open"),
                    "high": day_data.get("High"),
                    "low": day_data.get("Low"),
                    "close": day_data.get("Close"),
                    "volume": day_data.get("Volume")
                }
            }
            
            # 格式化提示词
            formatted_prompt = prompt.format(market_data=json.dumps(market_data, indent=2))
            
            # 调用LLM
            response = self.model_interface.get_completion(formatted_prompt)
            
            # 解析响应
            result = self._parse_trading_response(response)
            
            if self.verbose:
                print(f"📊 {day_data.get('trade_date')} 交易决策: {result.get('action', 'UNKNOWN')}")
                print(f"   信心度: {result.get('confidence', 0):.2f}")
            
            return result
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 交易模拟失败: {e}")
            return {"action": "HOLD", "confidence": 0.0, "error": str(e)}
    
    def _parse_trading_response(self, response: str) -> Dict[str, Any]:
        """解析LLM的交易响应"""
        try:
            # 尝试直接解析JSON
            if response.strip().startswith('{'):
                return json.loads(response.strip())
            
            # 查找JSON部分
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            # 如果无法解析，返回默认值
            return {"action": "HOLD", "confidence": 0.5, "reasoning": "解析失败"}
            
        except Exception as e:
            return {"action": "HOLD", "confidence": 0.0, "error": f"解析错误: {e}"}
    
    def calculate_trading_return(self, decisions: List[Dict[str, Any]]) -> float:
        """计算交易收益率"""
        if len(decisions) < 2:
            return 0.0
        
        total_return = 0.0
        position = 0  # 0: 无持仓, 1: 持有
        buy_price = 0.0
        
        for i, decision in enumerate(decisions):
            if i >= len(self.test_data):
                break
                
            current_price = self.test_data.iloc[i]['Close']
            action = decision.get('action', 'HOLD').upper()
            
            if action == 'BUY' and position == 0:
                # 买入
                position = 1
                buy_price = current_price
            elif action == 'SELL' and position == 1:
                # 卖出
                position = 0
                trade_return = (current_price - buy_price) / buy_price
                total_return += trade_return
        
        # 如果最后还有持仓，按最后价格计算
        if position == 1 and len(self.test_data) > 0:
            final_price = self.test_data.iloc[-1]['Close']
            final_return = (final_price - buy_price) / buy_price
            total_return += final_return
        
        return total_return
    
    def run_optimization_test(self, max_steps: int = 5) -> Dict[str, Any]:
        """运行优化测试"""
        if self.test_data is None:
            raise ValueError("请先加载测试数据")
        
        print(f"\n{'='*60}")
        print(f"🚀 开始单智能体Myerson-POMDP优化测试")
        print(f"{'='*60}")
        print(f"智能体类型: {self.agent_type}")
        print(f"股票代码: {self.ticker}")
        print(f"测试数据: {len(self.test_data)} 天")
        print(f"优化步数: {max_steps}")
        
        # 获取初始提示词
        current_prompt = self.get_default_prompt()
        
        results = []
        
        for step in range(max_steps):
            print(f"\n--- 优化步骤 {step + 1} ---")
            
            # 使用当前提示词进行交易模拟
            trading_decisions = []
            for _, day_data in self.test_data.iterrows():
                decision = self.simulate_trading_with_prompt(current_prompt, day_data.to_dict())
                trading_decisions.append(decision)
            
            # 计算收益率
            trading_return = self.calculate_trading_return(trading_decisions)
            
            # 模拟Myerson值（简化版本）
            myerson_value = trading_return + (step * 0.001)  # 添加小的变化
            
            # 计算奖励（基于收益率）
            reward = trading_return * 100  # 放大以便观察
            
            print(f"📈 交易收益率: {trading_return:.4f}")
            print(f"🎯 Myerson值: {myerson_value:.6f}")
            print(f"🏆 奖励: {reward:.6f}")
            
            # 记录结果
            step_result = {
                "step": step + 1,
                "prompt": current_prompt,
                "trading_return": trading_return,
                "myerson_value": myerson_value,
                "reward": reward,
                "decisions": trading_decisions,
                "prompt_length": len(current_prompt)
            }
            results.append(step_result)
            
            # 添加到评估器
            actions = [d.get('action', 'HOLD') for d in trading_decisions]
            self.evaluator.add_optimization_step(
                step=step + 1,
                myerson_value=myerson_value,
                myerson_reward=reward,
                prompt_length=len(current_prompt),
                trading_actions=actions
            )
            
            # 使用优化器更新提示词
            try:
                # 使用正确的update方法签名
                self.optimizer.update(
                    prompt=current_prompt,
                    traditional_score=reward * 100,
                    myerson_value=myerson_value,
                    task_examples=[]
                )
                
                # 获取优化后的提示词
                optimization_result = self.optimizer.get_optimized_prompt(
                    current_prompt=current_prompt,
                    current_traditional_score=reward * 100,
                    current_myerson_value=myerson_value,
                    task_examples=[],
                    num_candidates=1
                )
                
                if optimization_result.get("status") == "success" and "optimized_prompt" in optimization_result:
                    new_prompt = optimization_result["optimized_prompt"]
                    if new_prompt != current_prompt:
                        print(f"✅ 提示词已优化 (长度: {len(current_prompt)} → {len(new_prompt)})")
                        current_prompt = new_prompt
                    else:
                        print(f"🔄 提示词未变化")
                else:
                    print(f"⚠️ 优化状态: {optimization_result.get('status', 'unknown')}")
                    
            except Exception as e:
                print(f"❌ 优化过程出错: {e}")
        
        # 生成评估报告
        print(f"\n{'='*60}")
        print(f"📊 优化测试完成 - 生成评估报告")
        print(f"{'='*60}")
        
        # 使用真实的OHLCV数据进行评估
        evaluation_report = self.evaluator.generate_report(self.test_data.set_index('trade_date'))
        print(evaluation_report)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"single_agent_myerson_test_{self.agent_type}_{timestamp}.json"
        
        final_results = {
            "test_config": {
                "agent_type": self.agent_type,
                "ticker": self.ticker,
                "max_steps": max_steps,
                "data_days": len(self.test_data)
            },
            "optimization_steps": results,
            "evaluation_summary": {
                "total_steps": len(results),
                "best_return": max(r["trading_return"] for r in results),
                "final_return": results[-1]["trading_return"] if results else 0,
                "return_improvement": results[-1]["trading_return"] - results[0]["trading_return"] if len(results) > 1 else 0
            }
        }
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: {result_file}")
        
        return final_results

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="单智能体Myerson-POMDP优化测试")
    parser.add_argument("--agent", type=str, default="TRA", 
                       choices=["TRA", "TAA", "NAA", "FAA", "BOA", "BeOA", "NOA"],
                       help="要测试的智能体类型")
    parser.add_argument("--ticker", type=str, default="AAPL", help="股票代码")
    parser.add_argument("--steps", type=int, default=5, help="优化步数")
    parser.add_argument("--start_date", type=str, default="2025-05-13", help="开始日期")
    parser.add_argument("--end_date", type=str, default="2025-05-21", help="结束日期")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = SingleAgentMyersonPOMDPTester(
        agent_type=args.agent,
        ticker=args.ticker,
        verbose=args.verbose
    )
    
    # 加载测试数据
    if not tester.load_test_data(args.start_date, args.end_date):
        print("❌ 无法加载测试数据，退出")
        return 1
    
    # 运行优化测试
    try:
        results = tester.run_optimization_test(max_steps=args.steps)
        
        print(f"\n🎉 测试完成！")
        print(f"📈 最佳收益率: {results['evaluation_summary']['best_return']:.4f}")
        print(f"📊 收益改进: {results['evaluation_summary']['return_improvement']:.4f}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return 1

if __name__ == "__main__":
    exit(main()) 
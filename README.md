# 多智能体美股交易系统

基于大型语言模型的多层次智能体美股交易系统，实现了formulation中描述的框架。

## 系统架构

系统由多个专业化智能体组成，按照层次结构协作：

1. **初始分析层**：
   - NAA (News Analyst Agent)：新闻分析智能体
   - TAA (Technical Analyst Agent)：技术面分析智能体
   - FAA (Fundamental Analyst Agent)：基本面分析智能体

2. **综合分析层**：
   - STCA (Short-Term Comprehensive Analyst)：短期综合分析智能体
   - LTCA (Long-Term Comprehensive Analyst)：长期综合分析智能体

3. **风险评估与展望层**：
   - BOA (Bullish Outlook Agent)：看多展望智能体
   - BeOA (Bearish Outlook Agent)：看空展望智能体
   - NOA (Neutral/Observational Agent)：中立/观察智能体

4. **交易执行层**：
   - TRA (Trader Agent)：交易员智能体

5. **反思与优化层**：
   - 使用Myerson值计算各智能体贡献
   - 基于POMDP的提示优化器

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本运行

```bash
python run_multi_agent.py --tickers AAPL --start_date 2024-01-01 --end_date 2024-12-31
```

### 启用提示优化

默认启用提示优化（使用 --optimizer myerson_opro）。如果需要调整：

```bash
python run_multi_agent.py --tickers AAPL --start_date 2024-01-01 --end_date 2024-12-31 --optimize --exploration_rate 0.2
```

### 指定运行天数

```bash
python run_multi_agent.py --days 30
```

### 使用最佳参数

```bash
python run_multi_agent.py --load_best_params
```

### 切换模型

系统现在支持在智谱GLM-4和本地LM Studio模型之间切换：

```bash
# 查看当前使用的模型
python switch_model.py --status

# 切换到GLM-4
python switch_model.py glm

# 切换到本地LM Studio模型
python switch_model.py lm-studio

# 测试当前模型连接
python switch_model.py --test
```

详细说明请参考 [模型切换指南](docs/model_switching.md)。

### 数据下载

使用 `data/get_all_data.py` 脚本可以一站式下载指定股票在特定日期范围内的 OHLCV 数据、新闻数据以及相关的年度和季度财务报表数据。数据将自动保存到 `data/tickers/[ticker]/[ticker]_data.db` 数据库中。请确保在运行此脚本前，已经在 `config.py` 中配置了 `ALPHAVANTAGE_API_KEY`。

```bash
python data/get_all_data.py <股票代码> <开始日期> <结束日期>
```

**示例:**

下载 AAPL 在 2025 年 1 月 1 日至 2025 年 5 月 1 日期间的数据：

```bash
python data/get_all_data.py AAPL 2025-01-01 2025-05-01
```

### 完整参数说明

```
usage: run_multi_agent.py [-h] [--run_id RUN_ID] [--days DAYS] [--model MODEL]
                         [--start_date START_DATE] [--end_date END_DATE]
                         [--tickers TICKERS] [--starting_cash STARTING_CASH]
                         [--optimize] [--exploration_rate EXPLORATION_RATE]
                         [--optimizer OPTIMIZER] [--verbose]
                         [--load_best_params]

运行多智能体美股交易系统

可选参数:
  -h, --help            显示帮助信息并退出
  --run_id RUN_ID       运行ID，默认自动生成
  --days DAYS           运行天数，默认运行到环境结束
  --model MODEL         使用的模型，默认为gpt-4o
  --start_date START_DATE
                        起始日期，格式YYYY-MM-DD
  --end_date END_DATE   结束日期，格式YYYY-MM-DD
  --tickers TICKERS     股票代码，逗号分隔或多个参数，必填
  --starting_cash STARTING_CASH
                        初始资金
  --optimize            启用提示优化，默认启用
  --exploration_rate EXPLORATION_RATE
                        探索率
  --optimizer OPTIMIZER 使用的优化器类型，默认myerson_opro
  --verbose             详细输出
  --load_best_params    加载最佳参数
```

## 系统输出

运行结果将保存在 `reports/[run_id]/` 目录下，包括：

- `config.json`：运行配置
- `history.json`：完整的运行历史
- `summary.json`：运行摘要

日志文件保存在 `logs/multi_agent_run/[run_id]/` 目录下。

## 核心特性

1. **多层次智能体协作**：模拟人类专家团队的协作模式
2. **Myerson值贡献量化**：基于图限制博弈理论计算各智能体贡献
3. **POMDP提示优化**：动态优化智能体的提示参数，默认启用Myerson-OPRO优化
4. **反思机制**：智能体能够基于奖励进行反思和改进
5. **多模型支持**：支持智谱GLM-4和本地LM Studio模型

## 扩展开发

要添加新的智能体类型，需要：

1. 在 `agents/` 目录下创建新的智能体类文件
2. 在 `config.py` 中的 `AGENT_TYPES` 和 `AGENT_GRAPH` 中添加相应配置
3. 实现 `process()` 和 `reflect()` 方法 

要添加新的模型支持，需要：

1. 在 `config.py` 中的 `MODEL_CONFIGS` 中添加新模型配置
2. 在 `utils/model_interface.py` 中的 `ModelInterface` 类中添加相应的处理方法 

```bash
python run_multi_agent.py --start_date 2025-01-02 --end_date 2025-01-08 --stocks AAPL --plot --verbose
```

### 启用提示优化

```bash
python run_multi_agent.py --optimize --exploration_rate 0.2
```
#!/usr/bin/env python3
"""
优化的多智能体美股交易系统主运行脚本

基于有向图特性优化LLM调用，大幅提升执行效率
"""

import os
import argparse
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any
import json

from optimized_multi_agent_coordinator import OptimizedMultiAgentCoordinator
from config import TRADING_ENV_CONFIG, DEFAULT_MODEL, MODEL_CONFIG

# 自定义JSON编码器
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        import numpy as np
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="运行优化的多智能体美股交易系统")
    
    # 基本参数
    parser.add_argument("--tickers", nargs="+", default=["AAPL"], 
                       help="股票代码列表 (默认: AAPL)")
    parser.add_argument("--start_date", type=str, default="2024-01-01",
                       help="开始日期 YYYY-MM-DD (默认: 2024-01-01)")
    parser.add_argument("--end_date", type=str, default="2024-01-31", 
                       help="结束日期 YYYY-MM-DD (默认: 2024-01-31)")
    parser.add_argument("--days", type=int, default=5,
                       help="运行天数，0表示运行整个日期范围 (默认: 5)")
    
    # 系统配置
    parser.add_argument("--model", type=str, default=DEFAULT_MODEL,
                       help=f"使用的LLM模型 (默认: {DEFAULT_MODEL})")
    parser.add_argument("--starting_cash", type=float, default=TRADING_ENV_CONFIG["starting_cash"],
                       help=f"初始资金 (默认: ${TRADING_ENV_CONFIG['starting_cash']:,})")
    parser.add_argument("--trading_fee_rate", type=float, default=TRADING_ENV_CONFIG["trading_fee_rate"],
                       help=f"交易费率 (默认: {TRADING_ENV_CONFIG['trading_fee_rate']:.3f})")
    
    # 运行选项
    parser.add_argument("--verbose", action="store_true", default=False,
                       help="显示详细执行信息")
    parser.add_argument("--save_reports", action="store_true", default=True,
                       help="保存详细报告")
    parser.add_argument("--run_id", type=str, default=None,
                       help="运行ID，留空将自动生成")
    
    # 优化选项
    parser.add_argument("--enable_optimization", action="store_true", default=True,
                       help="启用智能体执行优化 (默认启用)")
    parser.add_argument("--parallel_execution", action="store_true", default=True,
                       help="启用并行执行 (默认启用)")
    parser.add_argument("--early_termination", action="store_true", default=True,
                       help="启用早期终止 (默认启用)")
    parser.add_argument("--enable_myerson_calculation", action="store_true", default=False,
                       help="启用Myerson值计算和POMDP优化 (警告：会大幅增加计算时间)")
    
    return parser.parse_args()

def validate_args(args):
    """验证参数"""
    from datetime import datetime
    
    try:
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d")
        
        if start_date >= end_date:
            raise ValueError("开始日期必须早于结束日期")
            
        if args.days < 0:
            raise ValueError("运行天数必须大于等于0")
            
        if args.starting_cash <= 0:
            raise ValueError("初始资金必须大于0")
            
        if not (0 <= args.trading_fee_rate <= 1):
            raise ValueError("交易费率必须在0-1之间")
            
    except ValueError as e:
        raise ValueError(f"参数验证失败: {e}")

def create_config(args) -> Dict[str, Any]:
    """创建配置字典"""
    config = {
        # 基本配置
        "stocks": args.tickers,
        "start_date": args.start_date,
        "end_date": args.end_date,
        "verbose": args.verbose,
        
        # 交易环境配置
        "starting_cash": args.starting_cash,
        "trading_fee_rate": args.trading_fee_rate,
        "price_window": TRADING_ENV_CONFIG.get("price_window", 20),
        "news_window": TRADING_ENV_CONFIG.get("news_window", 7),
        "fundamental_window": TRADING_ENV_CONFIG.get("fundamental_window", 4),
        
        # 模型配置
        "model_name": args.model,
        "model_config": MODEL_CONFIG,
        
        # 优化配置
        "enable_optimization": args.enable_optimization,
        "parallel_execution": args.parallel_execution,
        "early_termination": args.early_termination,
        "enable_myerson_calculation": args.enable_myerson_calculation,
        
        # 运行配置
        "run_id": args.run_id,
        "save_reports": args.save_reports
    }
    
    return config

def save_results(results: Dict[str, Any], run_id: str, ticker: str):
    """保存运行结果"""
    # 创建报告目录
    report_dir = f"reports/{run_id}"
    ticker_dir = f"{report_dir}/{ticker}"
    os.makedirs(ticker_dir, exist_ok=True)
    
    # 保存详细结果
    result_file = f"{ticker_dir}/detailed_results.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
    
    # 保存执行统计摘要
    summary = {
        "run_id": results.get("run_id", run_id),
        "ticker": ticker,
        "total_days": results.get("total_days", 0),
        "total_time": results.get("total_time", 0),
        "overall_stats": results.get("overall_stats", {}),
        "efficiency_summary": {
            "total_possible_calls": results.get("overall_stats", {}).get("total_possible_calls", 0),
            "total_actual_calls": results.get("overall_stats", {}).get("total_actual_calls", 0),
            "total_saved_calls": results.get("overall_stats", {}).get("total_llm_calls_saved", 0),
            "efficiency_improvement": results.get("overall_stats", {}).get("efficiency_improvement", 0)
        }
    }
    
    summary_file = f"{ticker_dir}/execution_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
    
    print(f"📁 结果已保存到: {ticker_dir}")

def print_efficiency_summary(results: List[Dict[str, Any]]):
    """打印效率汇总"""
    print(f"\n{'='*60}")
    print("🚀 优化效率汇总")
    print(f"{'='*60}")
    
    total_possible = 0
    total_actual = 0
    total_saved = 0
    total_time = 0
    total_myerson_time = 0
    total_subset_evaluations = 0
    total_actual_myerson_calls = 0
    myerson_enabled = False
    
    for result in results:
        if result.get("status") == "Success":
            stats = result["result"].get("overall_stats", {})
            total_possible += stats.get("total_possible_calls", 0)
            total_actual += stats.get("total_actual_calls", 0)
            total_saved += stats.get("total_llm_calls_saved", 0)
            total_time += result["result"].get("total_time", 0)
            total_myerson_time += stats.get("total_myerson_time", 0)
            total_subset_evaluations += stats.get("total_subset_evaluations", 0)
            total_actual_myerson_calls += stats.get("total_actual_myerson_calls", 0)
            if stats.get("myerson_calculation_enabled", False):
                myerson_enabled = True
    
    overall_efficiency = (total_saved / total_possible) * 100 if total_possible > 0 else 0
    
    print(f"📊 LLM调用统计:")
    print(f"  基础智能体可能调用数: {total_possible}")
    print(f"  基础智能体实际调用数: {total_actual}")
    print(f"  基础智能体节省调用数: {total_saved}")
    print(f"  基础智能体效率提升: {overall_efficiency:.1f}%")
    print(f"  总执行时间: {total_time:.2f}s")
    
    if myerson_enabled:
        myerson_reduction = total_subset_evaluations - total_actual_myerson_calls
        myerson_efficiency = (myerson_reduction / total_subset_evaluations) * 100 if total_subset_evaluations > 0 else 0
        
        print(f"\n🧮 Myerson值计算统计:")
        print(f"  理论子集评估次数: {total_subset_evaluations}")
        print(f"  实际Myerson LLM调用: {total_actual_myerson_calls}")
        print(f"  Myerson缓存优化节省: {myerson_reduction} ({myerson_efficiency:.1f}%)")
        print(f"  Myerson计算时间: {total_myerson_time:.2f}s")
        print(f"  Myerson计算占比: {total_myerson_time/total_time*100:.1f}%")
        
        # 整体优化统计
        total_theoretical_calls = total_possible + total_subset_evaluations
        total_actual_all_calls = total_actual + total_actual_myerson_calls
        overall_reduction = total_theoretical_calls - total_actual_all_calls
        overall_efficiency_all = (overall_reduction / total_theoretical_calls) * 100 if total_theoretical_calls > 0 else 0
        
        print(f"\n💡 整体优化效果:")
        print(f"  理论总LLM调用: {total_theoretical_calls}")
        print(f"  实际总LLM调用: {total_actual_all_calls}")
        print(f"  总计节省LLM调用: {overall_reduction}")
        print(f"  整体效率提升: {overall_efficiency_all:.1f}%")
        
        print(f"\n🎯 优化亮点:")
        print(f"  通过智能体输出缓存，Myerson值计算从 {total_subset_evaluations} 次LLM调用")
        print(f"  减少到只需 {total_actual_myerson_calls} 次，节省了 {myerson_reduction} 次调用!")
        
    elif total_saved > 0:
        print(f"\n💡 通过智能优化，节省了 {total_saved} 次LLM调用")
        print(f"   相当于减少了 {overall_efficiency:.1f}% 的计算成本和时间")

def main():
    """主函数"""
    print("🚀 优化的多智能体美股交易系统")
    print("="*50)
    print("基于有向图特性，智能优化LLM调用，大幅提升执行效率")
    print("="*50)
    
    # 解析参数
    args = parse_args()
    
    # 验证参数
    try:
        validate_args(args)
    except ValueError as e:
        print(f"❌ {e}")
        return 1
    
    # 创建运行ID
    if not args.run_id:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.run_id = f"optimized_run_{timestamp}"
    
    print(f"🆔 运行ID: {args.run_id}")
    print(f"📈 股票: {', '.join(args.tickers)}")
    print(f"📅 日期范围: {args.start_date} 到 {args.end_date}")
    print(f"💰 初始资金: ${args.starting_cash:,}")
    print(f"🔧 优化选项: {'启用' if args.enable_optimization else '禁用'}")
    print(f"🧮 Myerson值计算: {'启用' if args.enable_myerson_calculation else '禁用'}")
    
    if args.enable_myerson_calculation:
        print("⚠️  注意：Myerson值计算会大幅增加运行时间和LLM调用次数")
        print("   对于9个智能体，每天大约需要 2,304 次子集评估")
    
    # 处理所有股票
    results = []
    start_time = time.time()
    
    for ticker in args.tickers:
        print(f"\n{'='*20} 股票: {ticker} {'='*20}")
        
        try:
            # 创建配置
            config = create_config(args)
            config["stocks"] = [ticker]  # 每次只处理一个股票
            
            # 创建优化协调器
            coordinator = OptimizedMultiAgentCoordinator(config)
            
            # 运行系统
            print(f"开始为股票 {ticker} 运行优化的多智能体系统...")
            result = coordinator.run(days=args.days if args.days > 0 else None)
            
            # 保存结果
            if args.save_reports:
                save_results(result, args.run_id, ticker)
            
            # 记录成功结果
            results.append({
                "ticker": ticker,
                "status": "Success",
                "result": result
            })
            
            # 打印简要统计
            stats = result.get("overall_stats", {})
            efficiency = stats.get("efficiency_improvement", 0)
            saved_calls = stats.get("total_llm_calls_saved", 0)
            
            print(f"✅ {ticker} 处理完成")
            print(f"   效率提升: {efficiency:.1f}%")
            print(f"   节省LLM调用: {saved_calls}")
            
        except Exception as e:
            error_msg = str(e)
            print(f"❌ 为股票 {ticker} 运行模拟时出错: {error_msg}")
            
            results.append({
                "ticker": ticker,
                "status": "Run Error",
                "message": error_msg
            })
    
    # 总体汇总
    total_time = time.time() - start_time
    
    print(f"\n{'='*50}")
    print(f"所有股票处理完成。结果汇总目录: reports/{args.run_id}")
    print(f"总体结果摘要 ({len(args.tickers)} 个股票):")
    
    for result in results:
        status_icon = "✅" if result["status"] == "Success" else "❌"
        message = f"效率提升 {result['result'].get('overall_stats', {}).get('efficiency_improvement', 0):.1f}%" if result["status"] == "Success" else result["message"]
        print(f"  {status_icon} Ticker: {result['ticker']}, Status: {result['status']}, Message: {message}")
    
    # 打印效率汇总
    print_efficiency_summary(results)
    
    print(f"\n⏱️  总运行时间: {total_time:.2f}s")
    print("="*50)
    
    return 0

if __name__ == "__main__":
    exit(main()) 
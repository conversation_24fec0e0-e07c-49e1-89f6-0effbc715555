#!/usr/bin/env python3
"""
运行优化并详细观察每步提示词的变化
专门用于分析优化过程中提示词的演进
"""

import os
import sys
import random
import time
from datetime import date, timedelta
from typing import Dict, List, Any
import argparse
import sqlite3
import pandas as pd

# 项目路径设置
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入配置和工具
try:
    from config import MODEL_CONFIGS, BASE_DIR, DATA_DIR, DEFAULT_MODEL
    config_loaded = True
except ImportError:
    print("❌ 无法导入config.py，使用默认配置")
    MODEL_CONFIGS = {"mock_model": {"model_name": "mock_model"}}
    DEFAULT_MODEL = "mock_model"
    config_loaded = False

# 导入优化器和模型接口
utils_path = os.path.join(project_root, 'utils')
if utils_path not in sys.path:
    sys.path.insert(0, utils_path)

from myerson_opro_optimizer import MyersonOPROOptimizer
from model_interface import ModelInterface

def print_prompt_analysis(step_num: int, prompt: str, title: str = ""):
    """打印提示词的详细分析"""
    print(f"\n{'='*80}")
    print(f"📝 {title} - 步骤 {step_num}")
    print(f"{'='*80}")
    
    # 基本信息
    print(f"📊 提示词统计:")
    print(f"  长度: {len(prompt)} 字符")
    print(f"  行数: {len(prompt.split(chr(10)))} 行")
    print(f"  单词数: {len(prompt.split())} 个")
    
    # 关键词分析
    trading_keywords = ["BUY", "SELL", "HOLD", "买入", "卖出", "持有"]
    analysis_keywords = ["分析", "趋势", "技术", "指标", "风险", "confidence", "reasoning"]
    json_keywords = ["JSON", "json", "格式", "format"]
    
    found_trading = [kw for kw in trading_keywords if kw.upper() in prompt.upper()]
    found_analysis = [kw for kw in analysis_keywords if kw.lower() in prompt.lower()]
    found_json = [kw for kw in json_keywords if kw.lower() in prompt.lower()]
    
    print(f"  交易关键词: {found_trading}")
    print(f"  分析关键词: {found_analysis}")
    print(f"  格式关键词: {found_json}")
    
    # 提示词内容预览
    print(f"\n📄 提示词内容:")
    print(f"{'─'*80}")
    
    # 如果太长，只显示前后部分
    if len(prompt) > 800:
        lines = prompt.split('\n')
        if len(lines) > 20:
            # 显示前10行和后10行
            for i, line in enumerate(lines[:10]):
                print(f"{i+1:2d}│ {line}")
            print(f"   │ ... ({len(lines)-20} 行省略) ...")
            for i, line in enumerate(lines[-10:], len(lines)-10):
                print(f"{i+1:2d}│ {line}")
        else:
            # 显示前400和后400字符
            print(prompt[:400])
            print(f"\n... ({len(prompt)-800} 字符省略) ...\n")
            print(prompt[-400:])
    else:
        # 全部显示
        for i, line in enumerate(prompt.split('\n'), 1):
            print(f"{i:2d}│ {line}")
    
    print(f"{'─'*80}")

def compare_prompts(step_num: int, old_prompt: str, new_prompt: str):
    """比较两个提示词的差异"""
    print(f"\n🔍 步骤 {step_num} 提示词变化分析:")
    print(f"{'─'*50}")
    
    # 长度变化
    old_len = len(old_prompt)
    new_len = len(new_prompt)
    len_change = new_len - old_len
    
    print(f"📏 长度变化: {old_len} → {new_len} ({len_change:+d} 字符, {len_change/old_len*100:+.1f}%)")
    
    # 关键词变化分析
    trading_keywords = ["BUY", "SELL", "HOLD", "买入", "卖出", "持有", "交易", "决策"]
    analysis_keywords = ["分析", "趋势", "技术", "指标", "风险", "confidence", "reasoning", "理由", "原因"]
    format_keywords = ["JSON", "json", "格式", "format", "输出", "回复"]
    
    print(f"📊 关键词分析:")
    for category, keywords in [("交易", trading_keywords), ("分析", analysis_keywords), ("格式", format_keywords)]:
        old_count = sum(old_prompt.upper().count(kw.upper()) for kw in keywords)
        new_count = sum(new_prompt.upper().count(kw.upper()) for kw in keywords)
        change = new_count - old_count
        print(f"  {category}词汇: {old_count} → {new_count} ({change:+d})")
    
    # 结构变化分析
    old_lines = old_prompt.split('\n')
    new_lines = new_prompt.split('\n')
    old_non_empty = [line.strip() for line in old_lines if line.strip()]
    new_non_empty = [line.strip() for line in new_lines if line.strip()]
    
    print(f"📋 结构变化:")
    print(f"  有效行数: {len(old_non_empty)} → {len(new_non_empty)} ({len(new_non_empty)-len(old_non_empty):+d})")
    
    # 内容变化识别
    old_line_set = set(old_non_empty)
    new_line_set = set(new_non_empty)
    
    added_lines = new_line_set - old_line_set
    removed_lines = old_line_set - new_line_set
    common_lines = old_line_set & new_line_set
    
    print(f"  保留内容: {len(common_lines)} 行")
    print(f"  新增内容: {len(added_lines)} 行")
    print(f"  删除内容: {len(removed_lines)} 行")
    
    # 显示重要变化
    if added_lines:
        print(f"\n➕ 新增重要内容:")
        for i, line in enumerate(list(added_lines)[:3]):  # 只显示前3行
            if len(line) > 10:  # 忽略太短的行
                print(f"   {i+1}. {line[:100]}..." if len(line) > 100 else f"   {i+1}. {line}")
        if len(added_lines) > 3:
            print(f"   ... 还有 {len(added_lines)-3} 行新增内容")
    
    if removed_lines:
        print(f"\n➖ 删除重要内容:")
        for i, line in enumerate(list(removed_lines)[:3]):  # 只显示前3行
            if len(line) > 10:  # 忽略太短的行
                print(f"   {i+1}. {line[:100]}..." if len(line) > 100 else f"   {i+1}. {line}")
        if len(removed_lines) > 3:
            print(f"   ... 还有 {len(removed_lines)-3} 行删除内容")
    
    # 相似度分析
    if old_len > 0 and new_len > 0:
        # 简单的字符级相似度
        common_chars = sum(1 for c1, c2 in zip(old_prompt, new_prompt) if c1 == c2)
        max_len = max(old_len, new_len)
        similarity = common_chars / max_len if max_len > 0 else 0
        print(f"\n🎯 相似度分析:")
        print(f"  字符级相似度: {similarity:.2f}")
        
        if similarity > 0.8:
            print(f"  变化类型: 微调优化")
        elif similarity > 0.5:
            print(f"  变化类型: 中等调整")
        else:
            print(f"  变化类型: 重大改写")
    
    if not added_lines and not removed_lines:
        print(f"\n🔄 变化类型: 细微调整 - 可能是标点、格式或字词级别的修改")

def get_database_path(ticker=None):
    """获取数据库路径"""
    if ticker:
        ticker_dir = os.path.join(project_root, "data", "tickers", ticker.upper())
        os.makedirs(ticker_dir, exist_ok=True)
        return os.path.join(ticker_dir, f"{ticker.upper()}_data.db")
    else:
        return os.path.join(project_root, "data", "trading_data.db")

def get_ohlcv_data(ticker: str, start_date: str, end_date: str):
    """加载OHLCV数据"""
    db_path = get_database_path(ticker)
    try:
        conn = sqlite3.connect(db_path)
        query = """
        SELECT trade_date, Open, High, Low, Close, Adj_Close, Volume
        FROM ohlcv
        WHERE ticker = ? AND trade_date BETWEEN ? AND ?
        ORDER BY trade_date
        """
        df = pd.read_sql_query(query, conn, params=(ticker.upper(), start_date, end_date))
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df = df.set_index('trade_date')
        print(f"✅ 成功加载 {len(df)} 条 {ticker} 的OHLCV数据")
        return df
    except sqlite3.Error as e:
        print(f"❌ 数据库错误: {e}")
        return pd.DataFrame()
    finally:
        if 'conn' in locals():
            conn.close()

def simulate_trading(step: int, prompt: str, ohlcv_data: pd.DataFrame, model_interface: ModelInterface):
    """增强的交易模拟，智能选择关键交易日展示"""
    if ohlcv_data.empty:
        return 0.0, []
    
    initial_capital = 10000.0
    current_cash = initial_capital
    shares_held = 0
    decisions = []
    
    print(f"\n💹 步骤 {step} 交易模拟 ({len(ohlcv_data)}天):")
    print(f"{'─'*40}")
    
    # 智能选择显示的交易日
    total_days = len(ohlcv_data)
    if total_days <= 8:
        # 少于8天，全部显示
        display_indices = list(range(total_days))
    elif total_days <= 15:
        # 8-15天，显示首3天、中间2天、末2天
        mid = total_days // 2
        display_indices = [0, 1, 2, mid-1, mid, total_days-2, total_days-1]
        display_indices = sorted(list(set(display_indices)))  # 去重排序
    else:
        # 超过15天，显示首3天、中间3天、末2天
        mid = total_days // 2
        display_indices = [0, 1, 2, mid-1, mid, mid+1, total_days-2, total_days-1]
        display_indices = sorted(list(set(display_indices)))  # 去重排序
    
    # 计算价格统计
    price_min = ohlcv_data['Close'].min()
    price_max = ohlcv_data['Close'].max()
    price_change = (ohlcv_data['Close'].iloc[-1] - ohlcv_data['Close'].iloc[0]) / ohlcv_data['Close'].iloc[0]
    
    print(f"📊 市场概况: ${price_min:.2f}-${price_max:.2f}, 总变化{price_change:+.2%}")
    
    # 交易决策与执行
    for i in range(len(ohlcv_data)):
        row = ohlcv_data.iloc[i]
        trade_date = ohlcv_data.index[i]
        current_price = row['Close']
        
        # 构建市场上下文
        market_context = {
            "current_date": trade_date.strftime('%Y-%m-%d'),
            "current_price": float(current_price),
            "cash_available": float(current_cash),
            "shares_held": float(shares_held),
            "total_days": total_days,
            "day_number": i + 1
        }
        
        # LLM决策
        output_format = {
            "decision": "BUY/SELL/HOLD",
            "reason": "简短理由",
            "confidence": "0-1之间的数值"
        }
        
        try:
            llm_response = model_interface.get_structured_completion(
                prompt=prompt,
                system_message="你是专业交易员，根据市场数据做决策。",
                output_format=output_format
            )
            
            decision = llm_response.get('decision', 'HOLD').upper()
            confidence = float(llm_response.get('confidence', 0.5))
            reason = llm_response.get('reason', '未提供理由')[:30]  # 限制理由长度
        except:
            decision = 'HOLD'
            confidence = 0.5
            reason = '决策失败'
        
        decisions.append(decision)
        
        # 执行交易逻辑
        portfolio_value_before = current_cash + shares_held * current_price
        
        if decision == 'BUY' and current_cash > current_price:
            shares_to_buy = int(current_cash / current_price)
            if shares_to_buy > 0:
                cost = shares_to_buy * current_price
                current_cash -= cost
                shares_held += shares_to_buy
                action_result = f"买入{shares_to_buy}股"
            else:
                action_result = "资金不足"
        elif decision == 'SELL' and shares_held > 0:
            revenue = shares_held * current_price
            current_cash += revenue
            sold_shares = shares_held
            shares_held = 0
            action_result = f"卖出{sold_shares}股"
        else:
            action_result = f"持有{shares_held}股"
        
        # 计算当日组合价值
        portfolio_value_after = current_cash + shares_held * current_price
        daily_change = portfolio_value_after - portfolio_value_before
        
        # 只显示选中的关键日期
        if i in display_indices:
            print(f"  {trade_date.strftime('%m-%d')}: {decision} @${current_price:.2f} | {action_result} | 信心{confidence:.2f} | {reason}")
        
        # 在显示的日期之间插入省略号
        if i == display_indices[0] and len(display_indices) > 1 and display_indices[1] - display_indices[0] > 1:
            gap = display_indices[1] - display_indices[0] - 1
            if gap > 0:
                print(f"  ... (省略 {gap} 天)")
        elif i in display_indices[:-1]:
            next_idx = display_indices[display_indices.index(i) + 1]
            gap = next_idx - i - 1
            if gap > 0:
                print(f"  ... (省略 {gap} 天)")
    
    # 计算最终收益
    final_price = ohlcv_data['Close'].iloc[-1]
    final_value = current_cash + shares_held * final_price
    return_rate = (final_value - initial_capital) / initial_capital
    
    # 详细的结果分析
    print(f"\n📈 交易结果分析:")
    print(f"  💰 最终组合: 现金${current_cash:.0f} + {shares_held}股(${shares_held*final_price:.0f}) = ${final_value:.0f}")
    print(f"  📊 收益率: {return_rate:+.4f} ({return_rate*100:+.2f}%)")
    
    # 决策模式分析
    decision_counts = {d: decisions.count(d) for d in set(decisions)}
    most_common = max(decision_counts, key=decision_counts.get) if decision_counts else "NONE"
    print(f"  🎯 决策模式: {decision_counts}, 主要决策: {most_common}")
    
    # 交易活跃度
    buy_count = decisions.count('BUY')
    sell_count = decisions.count('SELL')
    hold_count = decisions.count('HOLD')
    activity_rate = (buy_count + sell_count) / len(decisions) if decisions else 0
    print(f"  🔄 活跃度: {activity_rate:.2f} (买入{buy_count}次, 卖出{sell_count}次, 持有{hold_count}次)")
    
    return return_rate, decisions

def run_optimization_with_analysis():
    """运行优化并详细分析每步提示词变化"""
    parser = argparse.ArgumentParser(description="运行优化并观察提示词变化")
    parser.add_argument("--ticker", type=str, default="AAPL", help="股票代码")
    parser.add_argument("--days", type=int, default=20, help="训练天数 - 增加到20天获得更丰富数据")
    parser.add_argument("--max_steps", type=int, default=6, help="优化步数 - 增加到6步观察更多变化")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    print(f"🔍 Myerson-OPRO 提示词优化过程观察 (增强版)")
    print(f"{'='*80}")
    print(f"📊 参数: {args.ticker}, {args.days}天, {args.max_steps}步")
    print(f"🎯 目标: 观察提示词在更长时间窗口下的优化轨迹")
    
    # 准备数据
    from datetime import datetime, timedelta
    try:
        # 智能选择日期范围
        db_path = get_database_path(args.ticker)
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT MAX(trade_date) FROM ohlcv WHERE ticker = ?', (args.ticker,))
        result = cursor.fetchone()
        
        if result[0]:
            latest_date = datetime.strptime(result[0], '%Y-%m-%d')
            end_date = latest_date.strftime('%Y-%m-%d')
            start_date = (latest_date - timedelta(days=args.days + 10)).strftime('%Y-%m-%d')
            print(f"📅 日期范围: {start_date} 至 {end_date}")
        else:
            raise Exception("无数据")
        conn.close()
    except:
        start_date = "2025-05-01"
        end_date = "2025-05-30"
        print(f"📅 使用默认日期: {start_date} 至 {end_date}")
    
    # 加载数据
    ohlcv_data = get_ohlcv_data(args.ticker, start_date, end_date)
    if ohlcv_data.empty:
        print(f"❌ 无法加载数据，退出")
        return
    
    if len(ohlcv_data) > args.days:
        ohlcv_data = ohlcv_data.tail(args.days)
    
    print(f"✅ 实际加载 {len(ohlcv_data)} 天数据")
    print(f"📈 价格范围: ${ohlcv_data['Close'].min():.2f} - ${ohlcv_data['Close'].max():.2f}")
    print(f"📊 波动率: {ohlcv_data['Close'].pct_change().std():.4f}")
    
    # 初始化模型接口
    try:
        if config_loaded:
            model_config = MODEL_CONFIGS[DEFAULT_MODEL]
            model_name = model_config.get("model_name", DEFAULT_MODEL)
        else:
            model_config = {"model_name": "mock_model"}
            model_name = "mock_model"
        
        model_interface = ModelInterface(model_name=model_name, config=model_config)
        print(f"✅ 模型接口初始化: {model_name}")
    except Exception as e:
        print(f"❌ 模型接口初始化失败: {e}")
        return
    
    # 初始提示词
    initial_prompt = """你是一个专业的交易员，请根据市场分析报告和新闻数据，给出明确的交易决策（买入/卖出/持有）和理由。请严格以JSON格式回复。
JSON格式要求：
{
  "decision": "BUY/SELL/HOLD",
  "price": "目标价格 (如果适用)",
  "reason": "交易决策的详细理由",
  "confidence": "信心度 (0-1之间的数值)"
}"""
    
    # 显示初始提示词
    print_prompt_analysis(0, initial_prompt, "初始提示词")
    
    # 初始化优化器
    optimizer = MyersonOPROOptimizer(
        agent_id="trader_analysis",
        agent_type="trader",
        model_interface=model_interface,
        max_optimization_steps=args.max_steps,
        history_prompts_k=3,  # 增加历史记录
        max_num_generated_instructions=3,  # 增加候选提示词数量
        plateau_patience=3  # 增加耐心
    )
    
    # 优化历史跟踪
    optimization_history = []
    
    # 优化循环
    current_prompt = initial_prompt
    current_myerson_value = 0.0
    
    print(f"\n🚀 开始优化循环...")
    
    for step in range(args.max_steps):
        print(f"\n\n🔄 优化步骤 {step + 1}/{args.max_steps}")
        print(f"{'='*80}")
        
        # 获取优化后的提示词
        optimization_result = optimizer.get_optimized_prompt(
            current_prompt=current_prompt,
            current_traditional_score=0.0,
            current_myerson_value=current_myerson_value,
            num_candidates=3  # 增加候选数量
        )
        
        optimized_prompt = optimization_result["optimized_prompt"]
        status = optimization_result["status"]
        
        print(f"📊 优化状态: {status}")
        
        # 分析提示词变化
        if step == 0:
            print_prompt_analysis(step + 1, optimized_prompt, "第1次优化后")
        else:
            compare_prompts(step + 1, current_prompt, optimized_prompt)
            if args.verbose:
                print_prompt_analysis(step + 1, optimized_prompt, f"第{step+1}次优化后")
        
        # 模拟交易测试新提示词
        new_myerson_value, decisions = simulate_trading(step + 1, optimized_prompt, ohlcv_data, model_interface)
        
        # 记录优化历史
        step_history = {
            "step": step + 1,
            "prompt_length": len(optimized_prompt),
            "myerson_value": new_myerson_value,
            "decisions": decisions.copy(),
            "decision_counts": {d: decisions.count(d) for d in set(decisions)},
            "diversity": len(set(decisions)) / len(decisions) if decisions else 0,
            "status": status
        }
        optimization_history.append(step_history)
        
        # 更新优化器
        optimizer.update(
            prompt=optimized_prompt,
            traditional_score=0.0,
            myerson_value=new_myerson_value
        )
        
        # 分析决策模式
        decision_counts = {d: decisions.count(d) for d in set(decisions)}
        diversity = len(set(decisions)) / len(decisions) if decisions else 0
        
        print(f"🎯 决策分析:")
        print(f"  决策分布: {decision_counts}")
        print(f"  多样性: {diversity:.2f}")
        print(f"  Myerson值: {new_myerson_value:+.6f}")
        print(f"  Myerson奖励: {optimizer.current_myerson_reward:+.6f}")
        
        # 显示进度对比
        if step > 0:
            prev_diversity = optimization_history[step-1]["diversity"]
            prev_myerson = optimization_history[step-1]["myerson_value"]
            print(f"  📈 变化: 多样性 {prev_diversity:.2f}→{diversity:.2f}, Myerson {prev_myerson:+.6f}→{new_myerson_value:+.6f}")
        
        # 更新当前状态
        current_prompt = optimized_prompt
        current_myerson_value = new_myerson_value
        
        # 检查停止条件
        if status in ["plateau", "max_steps"]:
            print(f"\n⏹️ 优化停止: {status}")
            break
    
    # 增强的最终总结
    print(f"\n\n📋 优化过程总结 (增强版)")
    print(f"{'='*80}")
    print(f"数据基础: {len(ohlcv_data)}天, 价格变化 {ohlcv_data['Close'].pct_change().sum():+.2%}")
    print(f"最终最佳提示词长度: {len(optimizer.best_prompt)} 字符")
    print(f"最终最佳Myerson值: {optimizer.best_myerson_value:+.6f}")
    print(f"最终最佳Myerson奖励: {optimizer.best_myerson_reward:+.6f}")
    
    # 优化轨迹分析
    print(f"\n📊 优化轨迹分析:")
    print(f"{'步骤':<4} {'长度':<6} {'Myerson值':<12} {'多样性':<8} {'主要决策':<10} {'状态'}")
    print(f"{'─'*60}")
    for i, h in enumerate(optimization_history):
        main_decision = max(h["decision_counts"], key=h["decision_counts"].get) if h["decision_counts"] else "N/A"
        print(f"{h['step']:<4} {h['prompt_length']:<6} {h['myerson_value']:+10.6f} {h['diversity']:>7.2f} {main_decision:<10} {h['status']}")
    
    # 显示最终最佳提示词
    print_prompt_analysis(9999, optimizer.best_prompt, "最终最佳提示词")
    
    # 性能改进分析
    if len(optimization_history) > 1:
        initial_myerson = optimization_history[0]['myerson_value']
        final_myerson = optimization_history[-1]['myerson_value']
        improvement = final_myerson - initial_myerson
        print(f"\n🎯 性能改进分析:")
        print(f"  初始Myerson值: {initial_myerson:+.6f}")
        print(f"  最终Myerson值: {final_myerson:+.6f}")
        print(f"  总体改进: {improvement:+.6f} ({improvement/abs(initial_myerson)*100:+.1f}%)" if initial_myerson != 0 else f"  总体改进: {improvement:+.6f}")
        
        # 发现最大改进步骤
        max_improvement_step = 0
        max_improvement = 0
        for i in range(1, len(optimization_history)):
            step_improvement = optimization_history[i]['myerson_value'] - optimization_history[i-1]['myerson_value']
            if step_improvement > max_improvement:
                max_improvement = step_improvement
                max_improvement_step = i + 1
        
        if max_improvement > 0:
            print(f"  最大改进步骤: 第{max_improvement_step}步 ({max_improvement:+.6f})")

if __name__ == "__main__":
    run_optimization_with_analysis() 
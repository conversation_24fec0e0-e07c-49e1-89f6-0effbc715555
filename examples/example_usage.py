#!/usr/bin/env python3
"""
修复后功能的使用示例

展示如何使用修复后的功能：
1. StockTradingEnv.get_state() 方法
2. MyersonOPROOptimizer 的稳定使用
3. 改进的数据处理
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def example_stock_trading_env():
    """演示 StockTradingEnv 的使用"""
    print("=" * 60)
    print("示例 1: StockTradingEnv 使用")
    print("=" * 60)
    
    from stock_trading_env import StockTradingEnv
    from config import TRADING_ENV_CONFIG
    
    # 创建交易环境配置
    config = TRADING_ENV_CONFIG.copy()
    config.update({
        "start_date": "2024-01-01",
        "end_date": "2024-01-10",
        "stocks": ["AAPL"],
        "starting_cash": 100000
    })
    
    print("🔧 创建交易环境...")
    env = StockTradingEnv(config)
    
    print("📊 获取初始状态...")
    state = env.get_state()  # 使用修复后的公共方法
    
    print(f"✅ 环境初始化成功")
    print(f"   当前日期: {state['date']}")
    print(f"   初始现金: ${state['cash']:,.2f}")
    print(f"   可用股票: {list(state['price_history'].keys())}")
    
    # 模拟一些交易操作
    print("\n🔄 执行交易操作...")
    actions = {"AAPL": 1.0}  # 全仓买入AAPL
    new_state, reward, done, info = env.step(actions)
    
    print(f"   交易后现金: ${new_state['cash']:,.2f}")
    print(f"   持仓: {new_state['positions']}")
    print(f"   日收益率: {reward:.4f}")
    
    return True

def example_myerson_opro_optimizer():
    """演示 MyersonOPROOptimizer 的使用"""
    print("\n" + "=" * 60)
    print("示例 2: MyersonOPROOptimizer 使用")
    print("=" * 60)
    
    from utils.myerson_opro_optimizer import MyersonOPROOptimizer
    
    print("🔧 创建优化器...")
    optimizer = MyersonOPROOptimizer(
        agent_id="DEMO_AGENT",
        agent_type="technical_analyst",
        model_interface=None,  # 演示中不使用实际模型
        max_optimization_steps=10,
        memory_size=50
    )
    
    print("📈 模拟优化过程...")
    
    # 模拟多轮优化
    prompts = [
        "分析技术指标并提供交易建议",
        "基于移动平均线分析股票趋势",
        "结合RSI和MACD指标进行技术分析",
        "使用布林带分析价格波动",
        "综合多个技术指标提供精确的交易信号"
    ]
    
    for i, prompt in enumerate(prompts):
        traditional_score = 0.6 + i * 0.05  # 模拟传统评分提升
        myerson_value = 0.1 + i * 0.03      # 模拟Myerson值提升
        
        print(f"\n   轮次 {i+1}:")
        print(f"     提示: {prompt[:50]}...")
        print(f"     传统评分: {traditional_score:.3f}")
        print(f"     Myerson值: {myerson_value:.3f}")
        
        # 调用修复后的update方法
        optimizer.update(prompt, traditional_score, myerson_value)
        
        print(f"     当前步数: {optimizer.current_step}")
        print(f"     最佳Myerson奖励: {optimizer.best_myerson_reward:.6f}")
    
    print(f"\n✅ 优化完成")
    print(f"   总步数: {optimizer.current_step}")
    print(f"   历史记录数: {len(optimizer.myerson_history)}")
    print(f"   最佳提示长度: {len(optimizer.best_prompt)} 字符")
    
    return True

def example_data_handling():
    """演示改进的数据处理"""
    print("\n" + "=" * 60)
    print("示例 3: 改进的数据处理")
    print("=" * 60)
    
    from stock_trading_env import StockTradingEnv
    
    print("🔧 测试不同的数据场景...")
    
    # 场景1: 正常数据
    print("\n📊 场景1: 正常数据加载")
    config1 = {
        "start_date": "2024-01-01",
        "end_date": "2024-01-05",
        "stocks": ["AAPL"],
        "starting_cash": 100000,
        "trading_fee_rate": 0.001,
        "price_window": 20,
        "news_window": 7
    }
    
    try:
        env1 = StockTradingEnv(config1)
        state1 = env1.get_state()
        print(f"   ✅ 成功加载 {len(env1.trading_days)} 个交易日")
        print(f"   📈 价格数据: {len(env1.price_data['AAPL'])} 条记录")
    except Exception as e:
        print(f"   ❌ 加载失败: {e}")
    
    # 场景2: 不存在的股票
    print("\n📊 场景2: 不存在的股票")
    config2 = {
        "start_date": "2024-01-01",
        "end_date": "2024-01-05",
        "stocks": ["NONEXISTENT"],
        "starting_cash": 100000,
        "trading_fee_rate": 0.001,
        "price_window": 20,
        "news_window": 7
    }
    
    try:
        env2 = StockTradingEnv(config2)
        state2 = env2.get_state()
        print(f"   ✅ 系统正确处理了不存在的股票")
        print(f"   📊 交易日数: {len(env2.trading_days)}")
        print(f"   💰 初始现金: ${state2['cash']:,.2f}")
    except Exception as e:
        print(f"   ❌ 处理失败: {e}")
    
    # 场景3: 极端日期范围
    print("\n📊 场景3: 极端日期范围")
    config3 = {
        "start_date": "2030-01-01",  # 未来日期
        "end_date": "2030-01-05",
        "stocks": ["AAPL"],
        "starting_cash": 100000,
        "trading_fee_rate": 0.001,
        "price_window": 20,
        "news_window": 7
    }
    
    try:
        env3 = StockTradingEnv(config3)
        state3 = env3.get_state()
        print(f"   ✅ 系统正确处理了未来日期")
        print(f"   📊 使用fallback交易日: {len(env3.trading_days)}")
    except Exception as e:
        print(f"   ❌ 处理失败: {e}")
    
    return True

def example_integration():
    """演示系统集成"""
    print("\n" + "=" * 60)
    print("示例 4: 系统集成演示")
    print("=" * 60)
    
    print("🔧 演示环境和优化器的集成使用...")
    
    # 创建环境
    from stock_trading_env import StockTradingEnv
    from utils.myerson_opro_optimizer import MyersonOPROOptimizer
    
    config = {
        "start_date": "2024-01-01",
        "end_date": "2024-01-05",
        "stocks": ["AAPL"],
        "starting_cash": 100000,
        "trading_fee_rate": 0.001,
        "price_window": 20,
        "news_window": 7
    }
    
    env = StockTradingEnv(config)
    optimizer = MyersonOPROOptimizer(
        agent_id="INTEGRATED_AGENT",
        agent_type="trader",
        model_interface=None
    )
    
    print("📊 模拟智能体决策循环...")
    
    for day in range(min(3, len(env.trading_days))):
        # 获取当前状态
        state = env.get_state()
        current_date = state['date']
        current_cash = state['cash']
        
        print(f"\n   📅 第{day+1}天 ({current_date}):")
        print(f"     💰 当前现金: ${current_cash:,.2f}")
        
        # 模拟智能体分析和决策
        analysis_prompt = f"分析{current_date}的市场状况并提供交易建议"
        traditional_score = 0.7 + day * 0.05
        myerson_value = 0.15 + day * 0.02
        
        # 更新优化器
        optimizer.update(analysis_prompt, traditional_score, myerson_value)
        
        # 模拟交易决策
        if day == 0:
            actions = {"AAPL": 1.0}  # 第一天买入
            print(f"     📈 决策: 全仓买入AAPL")
        elif day == 2:
            actions = {"AAPL": -1.0}  # 第三天卖出
            print(f"     📉 决策: 全仓卖出AAPL")
        else:
            actions = {}  # 持有
            print(f"     ⏸️ 决策: 持有")
        
        # 执行交易
        if actions:
            new_state, reward, done, info = env.step(actions)
            print(f"     💹 日收益率: {reward:.4f}")
            print(f"     💼 净资产: ${info['net_worth']:,.2f}")
        else:
            # 如果没有交易，手动推进一天
            env.current_day_index += 1
            if env.current_day_index < len(env.trading_days):
                env.current_date = env.trading_days[env.current_day_index]
    
    print(f"\n✅ 集成演示完成")
    print(f"   优化器步数: {optimizer.current_step}")
    print(f"   最终环境状态: 第{env.current_day_index+1}天")
    
    return True

def main():
    """运行所有示例"""
    print("🚀 修复后功能演示")
    print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    examples = [
        ("StockTradingEnv 使用", example_stock_trading_env),
        ("MyersonOPROOptimizer 使用", example_myerson_opro_optimizer),
        ("改进的数据处理", example_data_handling),
        ("系统集成演示", example_integration),
    ]
    
    results = []
    for name, func in examples:
        try:
            success = func()
            results.append((name, success))
        except Exception as e:
            print(f"❌ 示例 '{name}' 执行失败: {e}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 演示结果总结")
    print("=" * 60)
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} {name}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 个示例成功")
    
    if success_count == total_count:
        print("🎉 所有功能都正常工作！")
        return 0
    else:
        print("⚠️ 部分功能需要检查")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

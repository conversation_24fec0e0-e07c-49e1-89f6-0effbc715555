#!/usr/bin/env python3
"""
验证所有修复的测试脚本

测试：
1. 新闻数据迁移和加载
2. 增强日志系统
3. JSON序列化
4. 数据库查询修复
"""

import os
import sys
import json
import sqlite3
import subprocess
from datetime import datetime
from pathlib import Path

def test_news_data_migration():
    """测试新闻数据迁移"""
    print("=" * 60)
    print("测试 1: 新闻数据迁移")
    print("=" * 60)
    
    try:
        # 检查数据库中的新闻数据
        db_path = "data/tickers/AAPL/AAPL_data.db"
        if not os.path.exists(db_path):
            print("❌ 数据库文件不存在")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查新闻数据数量
        cursor.execute("SELECT COUNT(*) FROM news WHERE ticker = 'AAPL'")
        news_count = cursor.fetchone()[0]
        
        if news_count > 0:
            print(f"✅ 数据库中有 {news_count} 条新闻数据")
            
            # 检查日期范围
            cursor.execute("SELECT MIN(DATE(time_published)), MAX(DATE(time_published)) FROM news WHERE ticker = 'AAPL'")
            date_range = cursor.fetchone()
            print(f"   日期范围: {date_range[0]} 到 {date_range[1]}")
            
            conn.close()
            return True
        else:
            print("❌ 数据库中没有新闻数据")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_json_serialization():
    """测试JSON序列化"""
    print("\n" + "=" * 60)
    print("测试 2: JSON序列化")
    print("=" * 60)
    
    try:
        from utils.json_serializer import CustomJSONEncoder, safe_json_dumps
        import pandas as pd
        import numpy as np
        
        # 测试DataFrame序列化
        df = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=5),
            'price': [100.0, 101.5, 99.8, 102.3, 103.1],
            'volume': [1000, 1200, 800, 1500, 1100]
        })
        
        json_str = safe_json_dumps(df)
        print("✅ DataFrame序列化成功")
        
        # 测试Timestamp序列化
        timestamp = pd.Timestamp('2024-01-01 10:30:00')
        json_str = safe_json_dumps(timestamp)
        print("✅ Timestamp序列化成功")
        
        # 测试numpy数组序列化
        arr = np.array([1, 2, 3, 4, 5])
        json_str = safe_json_dumps(arr)
        print("✅ numpy数组序列化成功")
        
        # 测试复杂对象序列化
        complex_obj = {
            'dataframe': df,
            'timestamp': timestamp,
            'array': arr,
            'scalar': np.float64(3.14),
            'normal_data': {'key': 'value', 'number': 42}
        }
        
        json_str = safe_json_dumps(complex_obj)
        print("✅ 复杂对象序列化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON序列化测试失败: {e}")
        return False

def test_enhanced_logging():
    """测试增强日志系统"""
    print("\n" + "=" * 60)
    print("测试 3: 增强日志系统")
    print("=" * 60)
    
    try:
        from utils.enhanced_logger import EnhancedLogger
        
        # 创建测试日志目录
        test_log_dir = "test_logs"
        test_run_id = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger = EnhancedLogger(test_log_dir, test_run_id)
        
        # 测试各种日志记录
        logger.log_workflow_state("test_node", {"test": "data"})
        logger.log_agent_input("NAA", {"input": "test_data"}, "2024-01-01")
        logger.log_agent_output("NAA", {"output": "test_result"}, 1.5, "2024-01-01")
        logger.log_error("test_component", Exception("test error"), {"context": "test"})
        
        # 检查生成的日志文件
        log_summary = logger.get_log_summary()
        files_created = log_summary["files_created"]
        
        if len(files_created) >= 4:  # 至少应该有4个文件
            print(f"✅ 增强日志系统正常工作，生成了 {len(files_created)} 个日志文件")
            for file_info in files_created:
                print(f"   - {file_info['file']}: {file_info['lines']} 行")
            
            # 清理测试日志
            import shutil
            if os.path.exists(test_log_dir):
                shutil.rmtree(test_log_dir)
            
            return True
        else:
            print(f"❌ 日志文件数量不足: {len(files_created)}")
            return False
            
    except Exception as e:
        print(f"❌ 增强日志系统测试失败: {e}")
        return False

def test_stock_trading_env_news_loading():
    """测试股票交易环境新闻加载"""
    print("\n" + "=" * 60)
    print("测试 4: 股票交易环境新闻加载")
    print("=" * 60)
    
    try:
        from stock_trading_env import StockTradingEnv
        
        # 创建测试配置
        test_config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-05",
            "stocks": ["AAPL"],
            "starting_cash": 100000,
            "trading_fee_rate": 0.001,
            "price_window": 20,
            "news_window": 7
        }
        
        env = StockTradingEnv(test_config)
        
        # 检查新闻数据是否加载
        if hasattr(env, 'news_data') and env.news_data:
            total_news = 0
            for date_str, stocks_news in env.news_data.items():
                for stock, news_list in stocks_news.items():
                    total_news += len(news_list)
            
            if total_news > 0:
                print(f"✅ 成功加载了 {total_news} 条新闻数据")
                return True
            else:
                print("❌ 没有加载到新闻数据")
                return False
        else:
            print("❌ 新闻数据属性不存在")
            return False
            
    except Exception as e:
        print(f"❌ 股票交易环境测试失败: {e}")
        return False

def test_langgraph_coordinator_integration():
    """测试LangGraph协调器集成"""
    print("\n" + "=" * 60)
    print("测试 5: LangGraph协调器集成")
    print("=" * 60)
    
    try:
        # 运行一个简短的多智能体测试
        cmd = [
            "python", "run_multi_agent.py",
            "--tickers", "AAPL",
            "--days", "1",
            "--start_date", "2025-01-01",
            "--end_date", "2025-01-31",
            "--enhanced-data",
            "--verbose"
        ]
        
        print("运行1天多智能体测试...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            output = result.stdout
            
            # 检查关键输出
            success_indicators = [
                "NAA completed" in output or "NAA" in output,
                "TAA completed" in output or "TAA" in output,
                "TRA completed" in output or "TRA" in output,
                "Requested 1 days completed" in output or "day 1" in output.lower()
            ]
            
            successful_checks = sum(success_indicators)
            
            if successful_checks >= 3:
                print(f"✅ LangGraph协调器集成测试成功 ({successful_checks}/4 检查通过)")
                
                # 检查日志文件
                log_dirs = []
                if os.path.exists("logs/langgraph_run"):
                    log_dirs = [d for d in os.listdir("logs/langgraph_run") 
                               if os.path.isdir(os.path.join("logs/langgraph_run", d))]
                
                if log_dirs:
                    latest_log_dir = sorted(log_dirs)[-1]
                    log_path = os.path.join("logs/langgraph_run", latest_log_dir)
                    
                    log_files = [f for f in os.listdir(log_path) if f.endswith('.jsonl')]
                    print(f"   生成了 {len(log_files)} 个日志文件")
                
                return True
            else:
                print(f"❌ 集成测试部分失败 ({successful_checks}/4 检查通过)")
                print("输出:", output[:500])
                return False
        else:
            print(f"❌ 集成测试失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 集成测试超时")
        return False
    except Exception as e:
        print(f"❌ 集成测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始验证所有修复...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("新闻数据迁移", test_news_data_migration),
        ("JSON序列化", test_json_serialization),
        ("增强日志系统", test_enhanced_logging),
        ("股票交易环境新闻加载", test_stock_trading_env_news_loading),
        ("LangGraph协调器集成", test_langgraph_coordinator_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 发生未捕获异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有修复都已成功验证！")
        print("\n✅ 修复完成的功能:")
        print("   • 新闻数据成功迁移到数据库，NAA智能体可以正常运行")
        print("   • 增强日志系统生成所有必需的日志文件")
        print("   • JSON序列化robust处理pandas和numpy对象")
        print("   • 数据库查询使用正确的列名和函数")
        print("   • LangGraph协调器完整集成所有修复")
        return 0
    else:
        print("⚠️ 部分修复需要进一步调整")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 